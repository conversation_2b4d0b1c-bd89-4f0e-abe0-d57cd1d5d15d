
Imports System
Imports System.Collections.Generic
Imports System.Linq
Imports System.Reflection
Imports System.Windows.Forms
Imports System.Windows.Forms.VisualStyles.VisualStyleElement
Imports System.Xml
Imports Autodesk.Connectivity.Explorer.Extensibility
Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections
Imports Inventor
Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports VDFVCP = Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties
Imports VDFVF = Autodesk.DataManagement.Client.Framework.Vault.Forms
' These 5 assembly attributes must be specified or your extension will not load. 
'<Assembly: AssemblyCompany("MekSystems Oy")>
<Assembly: AssemblyProduct("SKCopy2ProjectCommandExtension")>
<Assembly: AssemblyDescription("SKCopy2Project App")>
' The extension ID needs to be unique for each extension.  
' Make sure to generate your own ID when writing your own extension. 
<Assembly: Autodesk.Connectivity.Extensibility.Framework.ExtensionId("6ab5941e-eb9f-4f1b-99a9-b9a958f512e4")>
' This number gets incremented for each Vault release.
<Assembly: Autodesk.Connectivity.Extensibility.Framework.ApiVersion("15.0")>
Namespace SKCopy2Project
    ''' <summary>
    ''' This class implements the IExtension interface, which means it tells Vault Explorer what 
    ''' commands and custom tabs are provided by this extension.
    ''' </summary>
    Public Class SKCopy2ProjectCommandExtension
        Implements IExplorerExtension
        Public Shared Components(7, 0) As String
        Public Shared ComponentsRow As Double = Nothing
        Public Shared Properties(14, 0) As String
        '    Public Shared m_MyForm As itabPreviewWindow
        Public Shared mFileName As String
        Public Shared exportselectedFile As Autodesk.Connectivity.WebServices.File = Nothing
        Public Shared exportselection As ISelection = Nothing
        Public Shared m_conn As Connection
        Public Shared filesMatrix(2, 0) As String 'masterID, ID, filename
        Public Shared lfMatrix(,) As String = Nothing
        Public Shared lfiMatrix(,) As String = Nothing
        Public Shared FIorPL As String
        Public Shared currUsrGroup As String
        Public Shared fromToMatrix(,) As String
        Public Shared usrLevelMatrix() As String
        Public Shared TOTi As Integer = 0
        Public Shared TOTii As Integer = 0
        Private SKstdNumberingcheme As Integer
        Public Shared itemMasterIds As Long()
        Public Shared filemasterIds As Long()
        Public Shared itemstoChange As Long()
        Public Shared itemstoChangeTo As Long()
        Public Shared filestoChange As Long()
        Public Shared filestoChangeTo As Long()
        Public Shared stateFromstr As String
        Public Shared stateFromstri As String
        Public Shared eContext As ICommandContext
        Public Shared SKAddBomRowSelections As System.Collections.Generic.List(Of Autodesk.Connectivity.Explorer.Extensibility.ISelection)
        Public Shared SKAddBomRowmgr As WebServiceManager
        Public Shared combobox_allowed_states As New Dictionary(Of Long, String)
        Public Shared combobox_allowed_states_count As Integer
        'Public Shared combobox_allowed_states As New List(Of String)
        Public Shared print2fileMatrix(,) As String = Nothing
        Public Shared print2fileMatrixlen As Integer = 8
        Public Shared SKprint2folderSelections As System.Collections.Generic.List(Of Autodesk.Connectivity.Explorer.Extensibility.ISelection)
        Public Shared SKprint2folderStateSelections As System.Collections.Generic.List(Of Autodesk.Connectivity.Explorer.Extensibility.ISelection)
#Region "IExtension Members"
        ''' <summary>
        ''' This function tells Vault Explorer what custom commands this extension provides.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <returns>A collection of CommandSites, which are collections of custom commands.</returns>
        Public Function CommandSites() As IEnumerable(Of CommandSite) Implements IExplorerExtension.CommandSites
            Try
                'create command for Item selection SKAddBomRow
                Dim SKAddBomRowCmdItem As New CommandItem("SKAddBomRowCommand", "SK Add Item Row to BOM...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.Item},
             .MultiSelectEnabled = True
            }
                AddHandler SKAddBomRowCmdItem.Execute, AddressOf SKAddBomRowCommandHandler
                'create command for Item selection SKPromote2NextState
                Dim SKPromote2NextStateCmdItem As New CommandItem("SKPromote2NextStateCommand", "SK Promote to Next State...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.Item},
             .MultiSelectEnabled = False
            }
                AddHandler SKPromote2NextStateCmdItem.Execute, AddressOf SKPromote2NextStateCommandHandler
                Dim SKCopyItemtoShipCmdItem As New CommandItem("SKCopyItemtoShip.itemContextMenu", "SK Copy SK-Items to Ship...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.Item},
             .MultiSelectEnabled = True
            }
                AddHandler SKCopyItemtoShipCmdItem.Execute, AddressOf SKCopyItemtoShipCmdItemCommandHandler
                ' this command is active when a File is selected
                ' this command is not active if there are multiple entities selected
                Dim SKCopy2ProjectCmdItem As New CommandItem("SKCopy2ProjectCommand", "SK Copy to Project...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.File, SelectionTypeId.FileVersion},
             .MultiSelectEnabled = False
            }
                Dim SKCopy2ProjectAisiCmdItem As New CommandItem("SKCopy2ProjectAisiCommand", "SK Copy to Project from 304 to 316...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.File, SelectionTypeId.FileVersion},
             .MultiSelectEnabled = False
            }
                Dim SKPrint2FolderCmdItem As New CommandItem("SKPrint2FolderCommand", "SK Publish 'To Yard'...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.File, SelectionTypeId.FileVersion},
             .MultiSelectEnabled = True
            }
                AddHandler SKPrint2FolderCmdItem.Execute, AddressOf SKPrint2FolderCommandHandler
                Dim SKPrint2FolderStateCmdItem As New CommandItem("SKPrint2FolderStateCommand", "SK Create PDF/DWF by State...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.File, SelectionTypeId.FileVersion},
             .MultiSelectEnabled = True
            }
                AddHandler SKPrint2FolderStateCmdItem.Execute, AddressOf SKPrint2FolderStateCommandHandler
                'this command copies item to project
                Dim SKCopyItemCmdItem As New CommandItem("SKCopyItemCommand", "SK Copy SK-Items to Ship...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.File, SelectionTypeId.FileVersion},
             .MultiSelectEnabled = True
            }
                'place component into inventor assy
                Dim SKPlace2InventorCmdItem As New CommandItem("SKPlace2InventorCommand", "SK Place to Inventor") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.File, SelectionTypeId.FileVersion},
             .MultiSelectEnabled = False
            }
                'Rename to standard
                Dim SKRename2StandardCmdItem As New CommandItem("SKRename2StandardCommand", "SK Rename to Standard...") With {
             .NavigationTypes = New SelectionTypeId() {SelectionTypeId.File, SelectionTypeId.FileVersion},
             .MultiSelectEnabled = False
            }
                AddHandler SKCopy2ProjectCmdItem.Execute, AddressOf SKCopy2ProjectCommandHandler
                AddHandler SKCopy2ProjectAisiCmdItem.Execute, AddressOf SKCopy2ProjectAisiCommandHandler
                AddHandler SKCopyItemCmdItem.Execute, AddressOf SKCopyItemCommandHandler
                'place component into inventor assy
                AddHandler SKPlace2InventorCmdItem.Execute, AddressOf SKPlace2InventorCmdItemCommandHandler
                'place component into inventor assy
                'rename to standard
                AddHandler SKRename2StandardCmdItem.Execute, AddressOf SKRename2StandardCmdItemCommandHandler
                ' Create a command site to hook the command to the Advanced toolbar
                Dim toolbarCmdSite As New CommandSite("SKCopy2ProjectCommand.Toolbar", "SK Copy to Project") With {
                 .Location = CommandSiteLocation.AdvancedToolbar,
                 .DeployAsPulldownMenu = False
                }
                toolbarCmdSite.AddCommand(SKCopy2ProjectCmdItem)
                Dim toolbarCmdSite3 As New CommandSite("SKCopyItem.Toolbar", "SK Copy SK-Items to Ship...") With {
                 .Location = CommandSiteLocation.AdvancedToolbar,
                 .DeployAsPulldownMenu = False
                }
                toolbarCmdSite3.AddCommand(SKCopyItemCmdItem)
                'place component into inventor assy
                Dim toolbarCmdSite1 As New CommandSite("SKPlace2InventorCommand", "SK Place to Inventor") With {
                 .Location = CommandSiteLocation.AdvancedToolbar,
                 .DeployAsPulldownMenu = False
                }
                toolbarCmdSite1.AddCommand(SKPlace2InventorCmdItem)
                'Rename to standard
                Dim toolbarCmdSite2 As New CommandSite("SKRename2StandardCommand", "SK Rename to Standard...") With {
                 .Location = CommandSiteLocation.AdvancedToolbar,
                 .DeployAsPulldownMenu = False
                }
                toolbarCmdSite2.AddCommand(SKRename2StandardCmdItem)
                'item bom  toolbar
                Dim ItemBomToolbarCmdSite As New CommandSite("SKPromote2NextStateCommand", "SK Promote to Next State...") With {
                .Location = CommandSiteLocation.ItemBomToolbar,
                .DeployAsPulldownMenu = False
               }
                ItemBomToolbarCmdSite.AddCommand(SKPromote2NextStateCmdItem)
                'create another command site to hook the command to the right-click menu for Items
                Dim itemContextCmdSite As New CommandSite("SKPromote2NextStateCommand.itemContextMenu", "SK Promote to Next State...") With {
                 .Location = CommandSiteLocation.ItemContextMenu,
                 .DeployAsPulldownMenu = False
                }
                itemContextCmdSite.AddCommand(SKPromote2NextStateCmdItem)
                Dim itemContextCmdSite1 As New CommandSite("SKAddBomRowCommand.itemContextMenu", "SK Add Item Row to BOM...") With {
                 .Location = CommandSiteLocation.ItemContextMenu,
                 .DeployAsPulldownMenu = False
                }
                itemContextCmdSite1.AddCommand(SKAddBomRowCmdItem)
                Dim itemContextCmdSite2 As New CommandSite("SKCopyItemtoShip.itemContextMenu", "SK Copy SK-Items to Ship..") With {
                 .Location = CommandSiteLocation.ItemContextMenu,
                 .DeployAsPulldownMenu = False
                }
                itemContextCmdSite2.AddCommand(SKCopyItemtoShipCmdItem)
                ' Create another command site to hook the command to the right-click menu for Files.
                Dim fileContextCmdSite As New CommandSite("SKCopy2ProjectCommand.FileContextMenu", "SK Copy to Project...") With {
                 .Location = CommandSiteLocation.FileContextMenu,
                 .DeployAsPulldownMenu = False
                }
                fileContextCmdSite.AddCommand(SKCopy2ProjectCmdItem)
                Dim fileContextCmdSite6 As New CommandSite("SKCopy2ProjectAisiCommand.FileContextMenu", "SK Copy to Project from 304 to 316...") With {
                 .Location = CommandSiteLocation.FileContextMenu,
                 .DeployAsPulldownMenu = False
                }
                fileContextCmdSite6.AddCommand(SKCopy2ProjectAisiCmdItem)
                Dim fileContextCmdSite3 As New CommandSite("SKCopyItemCommand.FileContextMenu", "SK Copy Sk-Item to Ship...") With {
                 .Location = CommandSiteLocation.FileContextMenu,
                 .DeployAsPulldownMenu = False
                }
                fileContextCmdSite3.AddCommand(SKCopyItemCmdItem)
                'place component into inventor assy
                Dim fileContextCmdSite1 As New CommandSite("SKPlace2InventorCommand.FileContextMenu", "SK Place to Inventor") With {
                 .Location = CommandSiteLocation.FileContextMenu,
                 .DeployAsPulldownMenu = False
                }
                fileContextCmdSite1.AddCommand(SKPlace2InventorCmdItem)
                'Rename to standard
                Dim fileContextCmdSite2 As New CommandSite("SKRename2StandardCommand", "SK Rename to Standard...") With {
                 .Location = CommandSiteLocation.FileContextMenu,
                 .DeployAsPulldownMenu = False
                }
                fileContextCmdSite2.AddCommand(SKRename2StandardCmdItem)
                Dim fileContextCmdSite4 As New CommandSite("SKPrint2FolderCommand", "SK Publish 'To Yard'...") With {
                 .Location = CommandSiteLocation.FileContextMenu,
                 .DeployAsPulldownMenu = False
                }
                fileContextCmdSite4.AddCommand(SKPrint2FolderCmdItem)
                Dim fileContextCmdSite5 As New CommandSite("SKPrint2FolderStateCommand", "SK Create PDF/DWF by State...") With {
                 .Location = CommandSiteLocation.FileContextMenu,
                 .DeployAsPulldownMenu = False
                }
                fileContextCmdSite5.AddCommand(SKPrint2FolderStateCmdItem)
                ' Now the custom command is available in 2 places.
                ' Gather the sites in a List.
                Dim sites As New List(Of CommandSite)()
                sites.Add(toolbarCmdSite)
                sites.Add(fileContextCmdSite)
                sites.Add(fileContextCmdSite6)
                sites.Add(toolbarCmdSite3)
                sites.Add(fileContextCmdSite3)
                sites.Add(toolbarCmdSite1)
                sites.Add(fileContextCmdSite1)
                sites.Add(toolbarCmdSite2)
                sites.Add(fileContextCmdSite2)
                sites.Add(fileContextCmdSite4)
                sites.Add(fileContextCmdSite5)
                sites.Add(ItemBomToolbarCmdSite)
                sites.Add(itemContextCmdSite)
                sites.Add(itemContextCmdSite1)
                sites.Add(itemContextCmdSite2)
                ' Return the list of CommandSites.
                Return sites
            Catch ex As Exception
            End Try
        End Function
        ''' <summary>
        ''' This function tells Vault Explorer what custom tabs this extension provides.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <returns>A collection of DetailTabs, each object represents a custom tab.</returns>
        Public Function DetailTabs() As IEnumerable(Of DetailPaneTab) Implements IExplorerExtension.DetailTabs
        End Function
        ''' <summary>
        ''' This function is called after the user logs in to the Vault Server.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <param name="application">Provides information about the running application.</param>
        Public Sub OnLogOn(application As IApplication) Implements IExplorerExtension.OnLogOn
        End Sub
        ''' <summary>
        ''' This function is called after the user is logged out of the Vault Server.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <param name="application">Provides information about the running application.</param>
        Public Sub OnLogOff(application As IApplication) Implements IExplorerExtension.OnLogOff
        End Sub
        ''' <summary>
        ''' This function is called before the application is closed.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <param name="application">Provides information about the running application.</param>
        Public Sub OnShutdown(application As IApplication) Implements IExplorerExtension.OnShutdown
            ' Although this function is empty for this project, it's still needs to be defined 
            ' because it's part of the IExtension interface.
        End Sub
        ''' <summary>
        ''' This function is called after the application starts up.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <param name="application">Provides information about the running application.</param>
        Public Sub OnStartup(application As IApplication) Implements IExplorerExtension.OnStartup
            ' Although this function is empty for this project, it's still needs to be defined 
            ' because it's part of the IExtension interface.
        End Sub
        ''' <summary>
        ''' This function tells Vault Exlorer which default commands should be hidden.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <returns>A collection of command names.</returns>
        Public Function HiddenCommands() As IEnumerable(Of String) Implements IExplorerExtension.HiddenCommands
            ' This extension does not hide any commands.
            Return Nothing
        End Function
        ''' <summary>
        ''' This function allows the extension to define special behavior for Custom Entity types.
        ''' Part of the IExtension interface.
        ''' </summary>
        ''' <returns>A collection of CustomEntityHandler objects.  Each object defines special behavior
        ''' for a specific Custom Entity type.</returns>
        Public Function CustomEntityHandlers() As IEnumerable(Of CustomEntityHandler) Implements IExplorerExtension.CustomEntityHandlers
            ' This extension does not provide special Custom Entity behavior.
            Return Nothing
        End Function
#End Region
        ''' <summary>
        ''' This is the function that is called whenever the custom command is executed.
        ''' </summary>
        ''' <param name="s">The sender object.  Usually not used.</param>
        ''' <param name="e">The event args.  Provides additional information about the environment.</param>
        ''' 
        Private Sub SKAddBomRowCommandHandler(s As Object, e As CommandItemEventArgs)
            Try
                SKAddBomRowmgr = e.Context.Application.Connection.WebServiceManager
                SKAddBomRowSelections = e.Context.ViewSelectionSet
                m_conn = e.Context.Application.Connection
                Dim m_form As New skAddBomRow
                m_form.Show()
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKCopyItemtoShipCmdItemCommandHandler(s As Object, e As CommandItemEventArgs)
            'copy sk-item to ship from selected vault item
            Try
                Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                Dim selections As List(Of ISelection) = e.Context.CurrentSelectionSet
                m_conn = e.Context.Application.Connection
                Try ' main
                    If e.Context.CurrentSelectionSet.Count = 0 Then
                        MessageBox.Show("Nothing Is selected")
                    Else
                        'get dialog
                        Dim selectedItem As ACW.Item = Nothing
                        Dim myform As New sk_copyItemI
                        'ship code
                        Try
                            If selections.First.TypeId = SelectionTypeId.Item Then
                                selectedItem = mgr.ItemService.GetLatestItemByItemNumber(selections.First.Label)
                            End If
                            myform.TextBoxShipFom.Text = selectedItem.ItemNum.Split("-").First
                        Catch ex As Exception
                        End Try
                        'Check if file connected
                        Try
                            For Each selection As ISelection In selections
                                selectedItem = mgr.ItemService.GetLatestItemByItemNumber(selection.Label)
                                'get item by master id
                                Dim primarylink As Long = Nothing
                                Try
                                    primarylink = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(selectedItem.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Primary).First.CldFileId
                                Catch ex As Exception
                                    primarylink = Nothing
                                End Try
                                If primarylink <> Nothing And myform.TextBoxShipFom.Text = selectedItem.ItemNum.Split("-").First Then
                                    'collect info to Grid
                                    Dim primaryfileselected As ACW.File = mgr.DocumentService.GetFileById(primarylink)
                                    Dim primaryfile As ACW.File = mgr.DocumentService.GetLatestFileByMasterId(primaryfileselected.MasterId)
                                    Try
                                        Dim row(14) As String
                                        row(0) = selectedItem.MasterId
                                        row(1) = selectedItem.Id
                                        row(2) = selectedItem.ItemNum
                                        Dim statedId As Long = selectedItem.LfCyc.LfCycStateId
                                        getLFIs(e)
                                        row(3) = ""
                                        For i As Integer = 0 To lfiMatrix.GetUpperBound(1)
                                            If statedId = lfiMatrix(1, i) Then
                                                row(3) = lfiMatrix(0, i).ToString
                                                Exit For
                                            End If
                                        Next
                                        row(4) = ""
                                        row(5) = primaryfile.MasterId
                                        row(6) = primaryfile.Id
                                        row(7) = primaryfile.Name
                                        row(8) = primaryfile.FileLfCyc.LfCycStateName
                                        Dim oFolder As Folder = mgr.DocumentService.GetFolderById(primaryfile.FolderId)
                                        row(9) = oFolder.FullName
                                        row(10) = oFolder.Name
                                        row(11) = ""
                                        If oFolder.Name.ToLower <> "furniture" Then
                                            row(11) = ""
                                            Dim oParentFolder As Folder = mgr.DocumentService.GetFolderById(oFolder.Id)
                                            Do Until oParentFolder.Name.ToLower = "furniture"
                                                If row(11) = "" Then
                                                    row(11) = oParentFolder.Name
                                                Else
                                                    row(6) = oParentFolder.Name & "/" & row(11)
                                                End If
                                                oParentFolder = mgr.DocumentService.GetFolderById(oParentFolder.ParId)
                                            Loop
                                        End If
                                        row(12) = oFolder.Id
                                        row(13) = ""
                                        row(14) = selectedItem.Title.ToString
                                        Dim gridrow As String() = New String() {row(0), row(1), row(2), row(3), row(4), row(5), row(6), row(7), row(8), row(9), row(10), row(11), row(12), row(13), row(14)}
                                        myform.DataGridView1.Rows.Add(gridrow)
                                    Catch ex As Exception
                                    End Try
                                    'Is this Item used in other Items (where used > 0) Type item
                                    Try
                                        Dim iBom As ACW.ItemBOM = m_conn.WebServiceManager.ItemService.GetItemBOMByItemIdAndDate(selectedItem.Id, Now, BOMTyp.Tip, BOMViewEditOptions.OmitChildren)
                                        Dim iBomrevs As Item() = iBom.ItemRevArray
                                        For Each itemrevision As Item In iBomrevs
                                            If itemrevision.MasterId <> selectedItem.MasterId Then
                                                'add row 
                                                Dim row(14) As String
                                                row(0) = itemrevision.MasterId
                                                row(1) = itemrevision.Id
                                                row(2) = itemrevision.ItemNum
                                                Dim statedId As Long = itemrevision.LfCyc.LfCycStateId
                                                getLFIs(e)
                                                row(3) = ""
                                                For i As Integer = 0 To lfiMatrix.GetUpperBound(1)
                                                    If statedId = lfiMatrix(1, i) Then
                                                        row(3) = lfiMatrix(0, i).ToString
                                                        Exit For
                                                    End If
                                                Next
                                                row(4) = selectedItem.ItemNum.ToString
                                                row(5) = ""
                                                row(6) = ""
                                                row(7) = ""
                                                row(8) = ""
                                                row(9) = ""
                                                row(10) = ""
                                                row(11) = ""
                                                row(12) = ""
                                                row(13) = ""
                                                row(14) = itemrevision.Title
                                                Dim gridrow As String() = New String() {row(0), row(1), row(2), row(3), row(4), row(5), row(6), row(7), row(8), row(9), row(10), row(11), row(12), row(13), row(14)}
                                                myform.DataGridView1.Rows.Add(gridrow)
                                            End If
                                        Next
                                    Catch ex As Exception
                                    End Try
                                    'Does this item have children which have "light bulb" on
                                    Try
                                        Dim iBom As ACW.ItemBOM = m_conn.WebServiceManager.ItemService.GetItemBOMByItemIdAndDate(selectedItem.Id, Now, BOMTyp.Tip, BOMViewEditOptions.OmitParents)
                                        Dim iBomrevs As Item() = iBom.ItemRevArray
                                        For Each itemrevision As Item In iBomrevs
                                            If itemrevision.MasterId <> selectedItem.MasterId Then
                                                'do we have filelinks
                                                Dim primarylinkrow As Long = Nothing
                                                Try
                                                    primarylinkrow = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(itemrevision.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Primary).First.CldFileId
                                                Catch ex As Exception
                                                    primarylinkrow = Nothing
                                                End Try
                                                Try
                                                    Dim row(14) As String
                                                    row(0) = itemrevision.MasterId
                                                    row(1) = itemrevision.Id
                                                    row(2) = itemrevision.ItemNum
                                                    Dim statedId As Long = itemrevision.LfCyc.LfCycStateId
                                                    getLFIs(e)
                                                    row(3) = ""
                                                    For i As Integer = 0 To lfiMatrix.GetUpperBound(1)
                                                        If statedId = lfiMatrix(1, i) Then
                                                            row(3) = lfiMatrix(0, i).ToString
                                                            Exit For
                                                        End If
                                                    Next
                                                    row(4) = "BOM Row in: " & selectedItem.ItemNum.ToString
                                                    If primarylinkrow <> Nothing Then 'And myform.TextBoxShipFom.Text = itemrevision.ItemNum.Split("-").First Then
                                                        'collect info to Grid
                                                        Dim primaryfileselectedRow As ACW.File = mgr.DocumentService.GetFileById(primarylinkrow)
                                                        Dim primaryfileRow As ACW.File = mgr.DocumentService.GetLatestFileByMasterId(primaryfileselectedRow.MasterId)
                                                        row(5) = primaryfileRow.MasterId
                                                        row(6) = primaryfileRow.Id
                                                        row(7) = primaryfileRow.Name
                                                        row(8) = primaryfileRow.FileLfCyc.LfCycStateName
                                                        Dim oFolder As Folder = mgr.DocumentService.GetFolderById(primaryfileRow.FolderId)
                                                        row(9) = oFolder.FullName
                                                        row(10) = oFolder.Name
                                                        row(11) = ""
                                                        If oFolder.Name.ToLower <> "furniture" Then
                                                            row(11) = ""
                                                            Dim oParentFolder As Folder = mgr.DocumentService.GetFolderById(oFolder.Id)
                                                            Try
                                                                Do Until oParentFolder.Name.ToLower = "furniture"
                                                                    If row(11) = "" Then
                                                                        row(11) = oParentFolder.Name
                                                                    Else
                                                                        row(11) = oParentFolder.Name & "/" & row(11)
                                                                    End If
                                                                    oParentFolder = mgr.DocumentService.GetFolderById(oParentFolder.ParId)
                                                                Loop
                                                            Catch ex As Exception
                                                                'did not find furniture
                                                                row(11) = oFolder.Name
                                                            End Try
                                                        End If
                                                        row(12) = oFolder.Id
                                                    Else
                                                        row(5) = ""
                                                        row(6) = ""
                                                        row(7) = ""
                                                        row(8) = ""
                                                        row(9) = ""
                                                        row(10) = ""
                                                        row(11) = ""
                                                        row(12) = ""
                                                    End If
                                                    row(13) = ""
                                                    row(14) = itemrevision.Title
                                                    Dim gridrow As String() = New String() {row(0), row(1), row(2), row(3), row(4), row(5), row(6), row(7), row(8), row(9), row(10), row(11), row(12), row(13), row(14)}
                                                    myform.DataGridView1.Rows.Add(gridrow)
                                                Catch
                                                End Try
                                            End If
                                        Next
                                    Catch ex As Exception
                                    End Try
                                End If
                            Next
                        Catch ex As Exception
                        End Try
                        Try
                            'show dialog
                            myform.Show()
                        Catch ex As Exception
                        End Try
                    End If
                Catch ex As Exception
                End Try 'main
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKPrint2FolderStateCommandHandler(s As Object, e As CommandItemEventArgs)
            'this download DWFs of selected drawings to selected folder and create pdfs if option selected
            'get selection
            eContext = e.Context
            m_conn = e.Context.Application.Connection
            SKprint2folderStateSelections = e.Context.CurrentSelectionSet
            Dim workingfolder As String = Nothing
            getLFs(e)
            Dim plotform As New SKPrint_to_folder_by_state_form
            'do we have bullzip installed?
            Try
                'Dim iniFile As String = (Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) & "\AppData\Roaming\PDF Writer\Bullzip PDF Printer\settings.ini")
                Dim iniFileExits As Boolean = False
                iniFileExits = System.IO.File.Exists(System.Environment.GetFolderPath(System.Environment.SpecialFolder.UserProfile) & "\AppData\Roaming\PDF Writer\Bullzip PDF Printer\settings.ini")
                If iniFileExits = False Then
                    MessageBox.Show("Is your BULLZIP printer installed and Options is launched once?" & vbCrLf & "Only DWF download is possible")
                    ' Exit Sub
                    plotform.CheckBox1.Checked = False
                    plotform.CheckBox1.Enabled = False
                End If
            Catch ex As Exception
            End Try
            Try
                If SKprint2folderStateSelections IsNot Nothing Then
                    plotform.Show()
                End If
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKPrint2FolderCommandHandler(s As Object, e As CommandItemEventArgs)
            '  MessageBox.Show("Print to folder is active")
            'get selection
            eContext = e.Context
            m_conn = e.Context.Application.Connection
            SKprint2folderSelections = e.Context.CurrentSelectionSet
            Dim myform As New SKPrint_to_folder_form
            Dim workingfolder As String = Nothing
            Try
                'Dim iniFile As String = (Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) & "\AppData\Roaming\PDF Writer\Bullzip PDF Printer\settings.ini")
                Dim iniFileExits As Boolean = False
                iniFileExits = System.IO.File.Exists(System.Environment.GetFolderPath(System.Environment.SpecialFolder.UserProfile) & "\AppData\Roaming\PDF Writer\Bullzip PDF Printer\settings.ini")
                If iniFileExits = False Then
                    MessageBox.Show("Is your BULLZIP printer installed and Options is launched once?" & vbCrLf & "Only DWG Creation is possible")
                    ' Exit Sub
                    myform.CheckBox2.Checked = False
                    myform.CheckBox2.Enabled = False
                End If
            Catch ex As Exception
            End Try
            Try
                If SKprint2folderSelections IsNot Nothing Then
                    Dim i As Long = 0
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    Dim oFile As ACW.File = Nothing
                    For Each selection As ISelection In SKprint2folderSelections
                        'get basic info from file.
                        ' Look of the File object.  How we do this depends on what is selected.
                        If selection.TypeId = SelectionTypeId.File Then
                            ' our ISelection.Id is really a File.MasterId
                            oFile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                        ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                            ' our ISelection.Id is really a File.Id
                            oFile = mgr.DocumentService.GetFileById(selection.Id)
                        End If
                        If oFile IsNot Nothing Then
                            If oFile.Name.ToString.Split(".").Last.ToLower = "dwg".ToLower Then
                                ReDim Preserve print2fileMatrix(print2fileMatrixlen, i)
                                print2fileMatrix(0, i) = oFile.MasterId ' master id
                                print2fileMatrix(1, i) = oFile.Id ' id
                                print2fileMatrix(2, i) = oFile.Name.ToString.Split(".").Last.ToLower 'extension
                                print2fileMatrix(3, i) = oFile.Name.ToString 'name
                                print2fileMatrix(4, i) = oFile.DesignVisAttmtStatus.ToString  'dwf status
                                print2fileMatrix(5, i) = getFileProperty("Revision", oFile) 'property value for Revision
                                print2fileMatrix(6, i) = getFileProperty("CUSTOMER_REVISION", oFile) ' property value of revision
                                If print2fileMatrix(4, i) <> "None" Then
                                    Dim visfiles() As ACW.FileAssocArray
                                    'then let us get the masterid of drawing
                                    visfiles = m_conn.WebServiceManager.DocumentService.GetDesignVisualizationAttachmentsByFileMasterIds(oFile.MasterId.ToSingleArray)
                                    print2fileMatrix(7, i) = visfiles(0).FileAssocs(0).CldFile.Id 'dwf id
                                End If
                                ' print2fileMatrix(8, i) = oFile.Name.ToString.Split(".").First.ToUpper() 'base name Mek: Original but does not work if filename is e.g.NB1400-166G.5.02.dwg
                                print2fileMatrix(8, i) = oFile.Name.ToString.ToLower.Replace(".dwg", "").ToUpper
                                i += 1
                            End If
                        End If
                    Next
                    Try
                        Dim folderid As Long = oFile.FolderId
                        Dim _folder As ACW.Folder = mgr.DocumentService.GetFolderById(folderid)
                        workingfolder = m_conn.WorkingFoldersManager.GetWorkingFolder(_folder.FullName.ToString).ToString()
                        workingfolder = workingfolder.Replace("Furniture", "To Yard")
                    Catch ex As Exception
                    End Try
                End If
            Catch ex As Exception
            End Try
            Try
                If workingfolder IsNot Nothing Then
                    myform.TextBox1.Text = workingfolder
                End If
                myform.Show()
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKPromote2NextStateCommandHandler(s As Object, e As CommandItemEventArgs)
            Try
                ' The Context part of the event args tells us information about what is selected.
                ' Run some checks to make sure that the selection is valid.
                'try to set e as connection
                m_conn = e.Context.Application.Connection
                eContext = e.Context
                ' Write_log("C:\temp\Promote_errors.log", "Starting SKPromote2NextStateCommandHandler")
                If e.Context.ViewSelectionSet.Count() = 0 Then
                    MessageBox.Show("Nothing Is selected")
                ElseIf e.Context.ViewSelectionSet.Count() > 1 Then
                    MessageBox.Show("This Function does Not support multiple selections")
                Else
                    Dim selectedItem As Item = Nothing
                    Dim selection As ISelection = Nothing
                    Try
                        selection = e.Context.PreviewSelectionSet.First()
                    Catch ex As Exception
                    End Try
                    If selection Is Nothing Then
                        selection = e.Context.ViewSelectionSet.First()
                    End If
                    If selection.TypeId <> SelectionTypeId.Item Then
                        selection = e.Context.ViewSelectionSet.First()
                    End If
                    '.CurrentSelectionSet.First() '.ViewSelectionSet.First()
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    If selection.TypeId = SelectionTypeId.Item Then
                        m_conn.WebServiceManager.AdminService.ClearCaches() 'clears cache. Not sure does this affect to get latest item/revision
                        selectedItem = mgr.ItemService.GetLatestItemByItemNumber(selection.Label)
                        Dim selectedItemRevision As VDF.Vault.Currency.Entities.ItemRevision = New VDF.Vault.Currency.Entities.ItemRevision(e.Context.Application.Connection, selectedItem)
                        'is this latest version (if not is in Edit mode or earlier version
                        Dim props As VDFVCP.PropertyDefinitionDictionary =
     m_conn.PropertyManager.GetPropertyDefinitions(
   VDF.Vault.Currency.Entities.EntityClassIds.Items,
       Nothing, VDFVCP.PropertyDefinitionFilter.IncludeAll)
                        Dim myKeyValPair As KeyValuePair _
                     (Of String, VDFVCP.PropertyDefinition)
                        Dim myisLatestVersion As VDFVCP.PropertyDefinition _
                                           = Nothing
                        Dim propDef As VDFVCP.PropertyDefinition
                        For Each myKeyValPair In props
                            ' Property definition from KeyValuePair
                            propDef = myKeyValPair.Value()
                            ' Using the display name to identify
                            ' the PropertyDefinition
                            If propDef.DisplayName =
                         "Latest Version" Then
                                'It is the PropertyDefinition
                                myisLatestVersion = propDef
                                Exit For
                            End If
                        Next
                        Dim strmyERPTYPE As String =
                                m_conn.PropertyManager.GetPropertyValue _
                                     (selectedItemRevision, myisLatestVersion, Nothing)
                        If strmyERPTYPE = False Then
                            MessageBox.Show("The Item Is In Edit Mode!")
                            Exit Sub
                        Else
                            getLFs(e) 'get all  file lf's
                            getLFIs(e) ' get all item lfäs
                            'fromToMatrixSub()
                            'FIorPLsub(e)
                            'state now
                            Dim lifeCycleDefId As Long = selectedItem.LfCyc.LfCycDefId
                            Dim lifeCyclestateId As Long = selectedItem.LfCycStateId
                            Dim lcDefs As LfCycDef() = m_conn.WebServiceManager.LifeCycleService.GetAllLifeCycleDefinitions()
                            Dim lifeCycleDefinition As LfCycDef
                            If lcDefs IsNot Nothing Then
                                lifeCycleDefinition = lcDefs.SingleOrDefault(Function(n) n.Id = lifeCycleDefId)
                            End If
                            Dim lifeCycleDef As Autodesk.Connectivity.WebServices.LfCycDef = m_conn.WebServiceManager.LifeCycleService.GetLifeCycleDefinitionsByIds(selectedItem.LfCyc.LfCycDefId.ToSingleArray()).First()
                            Dim stateMap As Dictionary(Of Long, Autodesk.Connectivity.WebServices.LfCycState) = lifeCycleDef.StateArray.ToDictionary(Function(n) n.Id)
                            Dim stateFrom As String = m_conn.WebServiceManager.LifeCycleService.GetLifeCycleStatesByIds(selectedItem.LfCycStateId.ToSingleArray).First.DispName
                            Dim state_orig_int_i As New Long
                            Dim state_orig_int As New Long
                            Dim state_to_int_i As New Long
                            Dim state_to_int As New Long
                            'Dim test As String = m_conn.WebServiceManager.LifeCycleService.GetLifeCycleStatesByIds(selectedItem.LfCycStateId.ToSingleArray).First
                            Dim stateFromId As Long
                            For i As Integer = 0 To lfMatrix.GetUpperBound(1)
                                If stateFrom = lfMatrix(0, i) Then
                                    stateFromId = lfMatrix(1, i)
                                End If
                            Next
                            Dim stateFromi As String = m_conn.WebServiceManager.LifeCycleService.GetLifeCycleStatesByIds(selectedItem.LfCycStateId.ToSingleArray).First.DispName
                            Dim stateFromIdi As Long
                            For i As Integer = 0 To lfiMatrix.GetUpperBound(1)
                                If stateFromi = lfiMatrix(0, i) Then
                                    stateFromIdi = lfiMatrix(1, i)
                                End If
                            Next
                            stateFromstr = stateFrom
                            stateFromstri = stateFromi
                            Dim stateTo As String = Nothing
                            Dim stateFromFrom As String = Nothing
                            Dim rownumber As Integer = 0
                            Dim colnumber As Integer = 0
                            'For i As Integer = 0 To fromToMatrix.GetUpperBound(0)
                            '    If fromToMatrix(i, 0) = stateFrom Then
                            '        rownumber = i
                            '        Exit For
                            '    End If
                            'Next
                            'For i As Integer = 0 To 8
                            '    If fromToMatrix(rownumber, i) = "True" And FIorPL = (Left(fromToMatrix(0, i), 2)) Then
                            '        stateTo = fromToMatrix(0, i)
                            '        colnumber = i
                            '        Exit For 'take first FI or PL
                            '    End If
                            'Next
                            'If stateTo Is Nothing Then
                            '    For i As Integer = 0 To 9   'used to be 8 changed to 9
                            '        If fromToMatrix(rownumber, i) = "True" Then
                            '            stateTo = fromToMatrix(0, i)
                            '            colnumber = i
                            '            Exit For
                            '        End If
                            '    Next
                            'End If
                            'what if I'm PL-Eng-Sr and stateto is PLForApproval ' changed 21.08.2018 MK
                            ''If currUsrGroup.ToLower = "PL-Eng-Sr".ToLower And stateTo.ToLower = "PL-ForApproval".ToLower Then
                            ''    'change stato to PL-Released
                            ''    stateTo = "PL-Released"
                            ''End If
                            'NOw we know state from and state to. when FI-WIP -> FI-Customer approval there can be also files in New
                            'Lets check if one row above is True
                            'If rownumber > 1 Then
                            '    If fromToMatrix(rownumber - 1, colnumber) = "True" Then
                            '        stateFromFrom = fromToMatrix(rownumber - 1, 0)
                            '    ElseIf fromToMatrix(1, colnumber) = "True" Then
                            '        stateFromFrom = fromToMatrix(1, 0)
                            '    End If
                            'End If
                            'Search next up and one down state id:s
                            'Dim state_orig_int_temp As Integer = state_orig_int + 1
                            'For i As Integer = 0 To lfMatrix.GetUpperBound(1)
                            '    If state_orig_int_temp = lfMatrix(1, i) Then
                            '        state_to_int = lfMatrix(1, i)
                            '    End If
                            'Next
                            'Dim state_orig_int_i_temp As Integer = state_orig_int_i + 1
                            'For i As Integer = 0 To lfiMatrix.GetUpperBound(1)
                            '    If state_orig_int_i_temp = lfiMatrix(0, i) Then
                            '        state_to_int_i = lfiMatrix(1, i)
                            '    End If
                            'Next
                            Dim _allowedLfCycStateTransitions2 As Long() = m_conn.WebServiceManager.LifeCycleService.GetAllowedLifeCycleStateTransitionIds 'for currUsr
                            Dim _LfCycTrans2 As ACW.LfCycTrans()
                            Dim stateToID_test As New List(Of Long)
                            Dim stateToID_test_str As New List(Of String)
                            Dim searchstateid As New Long
                            Dim searchstateid2 As Long = lifeCyclestateId - 1
                            _LfCycTrans2 = m_conn.WebServiceManager.LifeCycleService.GetLifeCycleStateTransitionsByIds(_allowedLfCycStateTransitions2)
                            combobox_allowed_states.Clear()
                            combobox_allowed_states_count = 0
                            'For Each lfcycTrans2 As ACW.LfCycTrans In _LfCycTrans2
                            '    If lfcycTrans2.FromId = lifeCyclestateId Then
                            '        stateToID_test.Add(lfcycTrans2.ToId)
                            '        For Each pair In stateMap
                            '            If pair.Key.ToString() = lfcycTrans2.ToId.ToString() Then
                            '                combobox_allowed_states.Add(pair.Key, pair.Value.DispName)
                            '            End If
                            '        Next
                            '    End If
                            'Next
                            Dim dllfolder As String = Assembly.GetExecutingAssembly().Location
                            Dim nextstate_fromini_fi As String = "New"
                            Dim nextstate_fromini_pl As String = "New"
                            Dim loc As String = ""
                            Dim states_file As String = Replace(Assembly.GetExecutingAssembly().Location, "SK-VaultExtension.dll", "SK-item-states.ini")
                            Using sr As StreamReader = New StreamReader(states_file)
                                While Not sr.EndOfStream
                                    datagridline = (sr.ReadLine())
                                    If datagridline.Contains(";") Then
                                        Dim array As String() = datagridline.Split(";"c)
                                        Dim test As String = array(0)
                                        If loc = "#FI" Then
                                            If test = stateFrom Then
                                                nextstate_fromini_fi = array(1)
                                            End If
                                        End If
                                        If loc = "#PL" Then
                                            If test = stateFrom Then
                                                nextstate_fromini_pl = array(1)
                                            End If
                                        End If
                                    Else
                                        loc = datagridline
                                    End If
                                End While
                            End Using
                            For Each lfcycTrans2 As ACW.LfCycTrans In _LfCycTrans2
                                If lfcycTrans2.FromId = lifeCyclestateId Then
                                    stateToID_test.Add(lfcycTrans2.ToId)
                                    For Each pair In stateMap
                                        If pair.Key.ToString() = lfcycTrans2.ToId.ToString() Then
                                            If nextstate_fromini_fi = pair.Value.DispName Then
                                                combobox_allowed_states.Add(pair.Key, pair.Value.DispName)
                                                combobox_allowed_states_count = 1
                                            End If
                                            If nextstate_fromini_fi <> nextstate_fromini_pl Then
                                                If nextstate_fromini_pl = pair.Value.DispName Then
                                                    combobox_allowed_states.Add(pair.Key, pair.Value.DispName)
                                                    combobox_allowed_states_count = 1
                                                End If
                                            End If
                                        End If
                                    Next
                                End If
                            Next
                            If combobox_allowed_states.Keys.ToString() = Nothing Then
                                MessageBox.Show("You are Not allowed To make state change.")
                                Exit Sub
                            End If
                            For Each stateToID_test_next As Long In stateToID_test
                                If stateToID_test_next > lifeCyclestateId Then
                                    If searchstateid < stateToID_test_next Then
                                        searchstateid = stateToID_test_next
                                        Exit For
                                    End If
                                End If
                            Next
                            For Each stateToID_test_next As Long In stateToID_test
                                If stateToID_test_next < lifeCyclestateId Then
                                    If searchstateid2 < stateToID_test_next Then
                                        searchstateid2 = stateToID_test_next
                                    End If
                                End If
                            Next
                            For Each pair In stateMap
                                If pair.Key.ToString() = searchstateid.ToString() Then
                                    stateTo = pair.Value.DispName
                                End If
                            Next
                            Dim stateToID As Long
                            For i As Integer = 0 To lfMatrix.GetUpperBound(1)
                                If stateTo = lfMatrix(0, i) Then
                                    stateToID = lfMatrix(1, i)
                                End If
                            Next
                            Dim stateToIDitem As Long
                            For i As Integer = 0 To lfiMatrix.GetUpperBound(1)
                                If stateTo = lfiMatrix(0, i) Then
                                    stateToIDitem = lfiMatrix(1, i)
                                End If
                            Next
                            For Each pair In stateMap
                                If pair.Key.ToString() = searchstateid2.ToString() Then
                                    stateFromFrom = pair.Value.DispName
                                End If
                            Next
                            Dim stateFromFromID As Long
                            If stateFromFrom IsNot Nothing Then
                                For i As Integer = 0 To lfMatrix.GetUpperBound(1)
                                    If stateFromFrom = lfMatrix(0, i) Then
                                        stateFromFromID = lfMatrix(1, i)
                                    End If
                                Next
                            End If
                            'POISTETTU TESTISSÄ TARKISTUKSET SALLITUSTA STATE MUUTOKSESTA. HAETAAN KAIKKI MAHDOLLISET STATE JA NÄYTETÄÄN NE
                            ''what is my permmission to?
                            'Dim _allowedLfCycStateTransitions As Long() = m_conn.WebServiceManager.LifeCycleService.GetAllowedLifeCycleStateTransitionIds 'for currUsr
                            'Dim _LfCycTrans As ACW.LfCycTrans()
                            '_LfCycTrans = m_conn.WebServiceManager.LifeCycleService.GetLifeCycleStateTransitionsByIds(_allowedLfCycStateTransitions)
                            'Dim amIallowed As Boolean = False
                            'For Each lfcycTrans As ACW.LfCycTrans In _LfCycTrans
                            '    If lfcycTrans.FromId = stateFromId And lfcycTrans.ToId = stateToID Then
                            '        '        '  MessageBox.Show("You are allowed To make state change from " & stateFrom & " To " & stateTo & ".")
                            '        amIallowed = True
                            '    End If
                            'Next
                            'If amIallowed = False Then
                            '    MessageBox.Show("You are Not allowed To make state change from " & stateFrom & " to " & stateTo & ".")
                            '    'Exit Sub
                            'End If
                            'POISTO LOPPUU
                            Dim m_form As New skPromote
                            Dim gridRow As String() = New String() {"Item", selectedItemRevision.EntityName, stateFrom, stateTo, selectedItem.MasterId} ' first item
                            m_form.DataGridView1.Rows.Add(gridRow)
                            'change last row to color blue
                            m_form.DataGridView1.Rows(m_form.DataGridView1.RowCount - 1).DefaultCellStyle.BackColor = System.Drawing.Color.LightSkyBlue
                            'primarylink 
                            Dim itemsvc As ACW.IItemService = Nothing
                            itemsvc = m_conn.WebServiceManager.ItemService
                            Dim modelfile As ACW.ItemFileAssoc() = itemsvc.GetItemFileAssociationsByItemIds(selectedItem.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Primary)
                            'now we should have primary file
                            If modelfile.Count > 0 Then
                                Dim file As ACW.File = Nothing
                                Dim fileid As Long = modelfile.First.CldFileId
                                file = m_conn.WebServiceManager.DocumentService.GetFileById(fileid)

                                Dim masterid As Long = file.MasterId
                                file = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterid)
                                If file.FileLfCyc.LfCycStateName = stateTo Then
                                    'goto
                                End If
                                'Dim fassocArr As FileAssocArray
                                Dim masterIDs As Long() = Nothing
                                Dim mIDs As Long() = Nothing
                                Dim ChangeStateMasterId As Long() = Nothing
                                traverseSub(file.MasterId, m_form.DataGridView1, stateFrom, stateFromFrom, stateTo)
                            End If
                            'what it is an excel or secondary iam or dwg file
                            Try
                                Dim secondaryFiles As ACW.ItemFileAssoc() = itemsvc.GetItemFileAssociationsByItemIds(selectedItem.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Secondary)
                                If secondaryFiles.Count > 0 Then
                                    For i As Integer = 0 To secondaryFiles.Count - 1
                                        Dim oSecFile As ACW.ItemFileAssoc = secondaryFiles(i)
                                        If oSecFile.FileName.Split(".").Last.ToLower Like "xls*" Or oSecFile.FileName.Split(".").Last.ToLower = "iam" Or oSecFile.FileName.Split(".").Last.ToLower = "dgn" Then
                                            Dim file As ACW.File = Nothing
                                            Dim fileid As Long = oSecFile.CldFileId
                                            file = m_conn.WebServiceManager.DocumentService.GetFileById(fileid)
                                            Dim masterid As Long = file.MasterId
                                            file = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterid)
                                            If file.FileLfCyc.LfCycStateName = stateTo Then
                                                'goto ?
                                            End If
                                            'Dim fassocArr As FileAssocArray
                                            Dim masterIDs As Long() = Nothing
                                            Dim mIDs As Long() = Nothing
                                            Dim ChangeStateMasterId As Long() = Nothing
                                            'the whole getting routine is done again
                                            traverseSub(file.MasterId, m_form.DataGridView1, stateFrom, stateFromFrom, stateTo)
                                        End If
                                    Next
                                End If
                            Catch ex As Exception
                            End Try
                            Try
                                'what if item have active BOM items
                                Dim _itemassocs As ItemAssoc() = itemsvc.GetItemBOMAssociationsByItemIds(selectedItem.Id.ToSingleArray, False)
                                If _itemassocs IsNot Nothing Then
                                    For Each _itemassoc As ItemAssoc In _itemassocs
                                        If _itemassoc.IsIncluded = True Then
                                            'get sub item
                                            selectedItem = mgr.ItemService.GetLatestItemByItemMasterId(_itemassoc.CldItemMasterID)
                                            selectedItemRevision = New VDF.Vault.Currency.Entities.ItemRevision(e.Context.Application.Connection, selectedItem)
                                            Dim stateFromSub As String = m_conn.WebServiceManager.LifeCycleService.GetLifeCycleStatesByIds(selectedItem.LfCycStateId.ToSingleArray).First.DispName
                                            If stateFromSub = stateFrom Or stateFromSub = stateFromFrom Then
                                                'write item info to grid
                                                gridRow = New String() {"Item", selectedItemRevision.EntityName, stateFromSub, stateTo, selectedItem.MasterId} ' first item
                                                m_form.DataGridView1.Rows.Add(gridRow)
                                                'change last row to color blue
                                                m_form.DataGridView1.Rows(m_form.DataGridView1.RowCount - 1).DefaultCellStyle.BackColor = System.Drawing.Color.LightSkyBlue
                                                'get primary file
                                                modelfile = itemsvc.GetItemFileAssociationsByItemIds(selectedItem.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Primary)
                                                Dim fileid As Long = modelfile.First.CldFileId
                                                Dim File As ACW.File = m_conn.WebServiceManager.DocumentService.GetFileById(fileid)
                                                Dim masterid As Long = File.MasterId
                                                File = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterid)
                                                If File.FileLfCyc.LfCycStateName = stateTo Then
                                                    'goto ?
                                                End If
                                                'traverse through files
                                                traverseSub(File.MasterId, m_form.DataGridView1, stateFrom, stateFromFrom, stateTo)
                                            End If
                                        End If
                                    Next
                                End If
                            Catch ex As Exception
                            End Try
                            Try
                                Dim itemMasterIDi As Integer = 0
                                Dim fileMasterIDi As Integer = 0
                                'create item and file matrises from grid
                                For row As Integer = 0 To m_form.DataGridView1.RowCount - 1
                                    If m_form.DataGridView1(0, row).Value = "Item" Then
                                        'add item master id to itemmasterIDs
                                        ReDim Preserve itemMasterIds(itemMasterIDi)
                                        itemMasterIds(itemMasterIDi) = m_form.DataGridView1(4, row).Value
                                        itemMasterIDi += 1
                                    Else
                                        ReDim Preserve filemasterIds(fileMasterIDi)
                                        filemasterIds(fileMasterIDi) = m_form.DataGridView1(4, row).Value
                                        fileMasterIDi += 1
                                    End If
                                Next
                                'sort arrays
                                If itemMasterIds IsNot Nothing Then
                                    Array.Sort(itemMasterIds)
                                End If
                                If filemasterIds IsNot Nothing Then
                                    Array.Sort(filemasterIds)
                                End If
                            Catch ex As Exception
                            End Try
                            'Tarkistus multiple item in datagrid tähän ehkä pl-release pois?
                            For intI As Integer = m_form.DataGridView1.Rows.Count - 1 To 0 Step -1
                                For intJ As Integer = m_form.DataGridView1.Rows.Count - 1 To intI + 1 Step -1
                                    If m_form.DataGridView1.Rows(intI).Cells(4).Value = m_form.DataGridView1.Rows(intJ).Cells(4).Value Then
                                        m_form.DataGridView1.Rows.RemoveAt(intJ)
                                    End If
                                Next intJ
                                'tähän oma 
                            Next intI
                            Try
                                'next create changestate id pairs (masterid and stateto id)
                                ReDim itemstoChange(0)
                                ReDim itemstoChangeTo(0)
                                Dim iii As Integer = 1
                                If itemMasterIds IsNot Nothing Then
                                    itemstoChange(0) = itemMasterIds(0) 'mid
                                    itemstoChangeTo(0) = stateToIDitem
                                    For i As Integer = 1 To itemMasterIds.Count - 1
                                        If itemMasterIds(i - 1) <> itemMasterIds(i) Then
                                            ReDim Preserve itemstoChange(iii)
                                            ReDim Preserve itemstoChangeTo(iii)
                                            itemstoChange(iii) = itemMasterIds(i) 'mid
                                            itemstoChangeTo(iii) = stateToIDitem
                                            iii += 1
                                        End If
                                    Next
                                End If
                                ReDim filestoChange(0)
                                ReDim filestoChangeTo(0)
                                If filemasterIds IsNot Nothing Then
                                    filestoChange(0) = filemasterIds(0) 'mid
                                    filestoChangeTo(0) = stateToID
                                    iii = 1
                                    For i As Integer = 1 To filemasterIds.Count - 1
                                        If filemasterIds(i - 1) <> filemasterIds(i) Then
                                            ReDim Preserve filestoChange(iii)
                                            ReDim Preserve filestoChangeTo(iii)
                                            filestoChange(iii) = filemasterIds(i) 'mid
                                            filestoChangeTo(iii) = stateToID
                                            iii += 1
                                        End If
                                    Next
                                End If
                            Catch ex As Exception
                            End Try
                            If SKCopy2Project.SKCopy2ProjectCommandExtension.combobox_allowed_states_count = 1 Then
                                m_form.Show()
                            Else
                                MessageBox.Show("No rights to promote objects")
                                Exit Sub
                            End If
                            '   Dim selectedItemState As Long = selectedItem.LfCycStateId
                        End If
                    End If
                End If
            Catch ex As Exception
                Write_log("C:\temp\Promote_errors.log", ex.Message.ToString())
            End Try
        End Sub
        Private newStatusFiles As New List(Of String)
        ' Add a cache for file information to reduce repeated web service calls
        Private fileCache As New Dictionary(Of Long, ACW.File)
        Private stateCache As New Dictionary(Of Long, String)
        Function traverseSub(masterId As Long, ByVal gridview As DataGridView, ByVal statefrom As String, ByVal statefromfrom As String, ByVal stateTo As String)
            ' Clear the collection at the start of the main traversal
            newStatusFiles = New List(Of String)
            Try
                'get info and parents of this file
                Dim currfile As ACW.File = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterId)
                'add info to grid
                Dim cldName As String = currfile.Name
                Dim cldnameBase As String = cldName.Split(".").First
                Dim cldLFname As String = currfile.FileLfCyc.LfCycStateName

                If cldLFname.ToLower = "pl-released" Then Exit Function
                ' Check for "new" status
                Dim validNewStates As String() = {"FI-WIP", "FI-For Review", "PL-WIP"}
                If cldLFname.ToLower = "new" Then
                    Debug.WriteLine("Found file with 'New' status: " & cldName)
                    If Not validNewStates.Contains(stateTo) Then
                        newStatusFiles.Add(cldName)
                    End If
                End If

                If cldLFname = statefrom Or cldLFname = statefromfrom Then ' is statefrom or statefromfrom
                    Dim gridRow As String() = New String() {"", cldName, cldLFname, stateTo, masterId}
                    Debug.WriteLine($"[{DateTime.Now:HH:mm:ss}] tSub {cldName} St:{cldLFname}")
                    gridview.Rows.Add(gridRow)
                End If
                'if it is statefromfrom
                'find parents and add to the grid - get ACTUAL parent associations for this specific file
                Dim fassocarr As FileAssocArray
                Dim fileIdToCheck As Long = If(referencedFileId > 0, referencedFileId, currFile.Id)
                fassocarr = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds({fileIdToCheck}, FileAssociationTypeEnum.Dependency, False, FileAssociationTypeEnum.None, False, True, True).First
                If fassocarr.FileAssocs IsNot Nothing Then
                    For Each assoc As FileAssoc In fassocarr.FileAssocs
                        Dim parName As String = assoc.ParFile.Name
                        Dim parNameBase As String = Split(parName, ".").First
                        Dim parLFname As String = assoc.ParFile.FileLfCyc.LfCycStateName
                        If (parLFname = statefrom Or parLFname = statefromfrom) And cldnameBase = parNameBase Then
                            Dim gridRow As String() = New String() {"", parName, parLFname, stateTo, assoc.ParFile.MasterId}
                            Debug.WriteLine($"Loop[{DateTime.Now:HH:mm:ss}] tSub {parName} St:{parLFname}")
                            gridview.Rows.Add(gridRow)
                        End If
                    Next
                End If
                'direct childs - get ACTUAL file associations for the current file, not latest
                ' If we have a specific file ID (referencedFileId), use that; otherwise use currFile.Id
                Dim fileIdToCheck As Long = If(referencedFileId > 0, referencedFileId, currFile.Id)
                fassocarr = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds({fileIdToCheck}, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, True, False, True).First
                'now we have the ACTUAL child models referenced by this specific file version
                If fassocarr.FileAssocs IsNot Nothing Then
                    For Each fassoc As FileAssoc In fassocarr.FileAssocs
                        ' Pass the ACTUAL file ID that's referenced, not the latest
                        traverseSubSub(fassoc.CldFile.MasterId, gridview, statefrom, statefromfrom, stateTo, fassoc.CldFile.Id)
                    Next
                Else
                End If
                '   Array.Sort(masterIDs)
            Catch ex As Exception
            End Try
            ' Show message at the end of the main traversal
            If newStatusFiles.Count > 0 Then
                Dim result As DialogResult = MessageBox.Show("Files with 'New' status: " & String.Join(vbCrLf, newStatusFiles), "Warning - Exiting Function", MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                'MessageBox.Show("Files with 'New' status: " & String.Join(vbCrLf, newStatusFiles), "Warning - Exiting Function")

                If result = DialogResult.Yes Then
                    System.Environment.Exit(0)
                End If

            End If
            Return "ok"
        End Function
        Function traverseSubSub(masterId As Long, ByVal gridview As DataGridView, ByVal statefrom As String, ByVal statefromfrom As String, ByVal stateTo As String, Optional referencedFileId As Long = 0) As String
            Try
                ' Get full file info for current masterId
                Dim currFile As ACW.File = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterId)
                Dim cldName As String = currFile.Name
                Dim cldNameBase As String = cldName.Split("."c).First
                Dim cldLFname As String = currFile.FileLfCyc.LfCycStateName
                If cldLFname.ToLower() = "pl-released" Then Return "ok"

                Dim validNewStates As String() = {"FI-WIP", "FI-For Review", "PL-WIP"}
                If cldLFname.ToLower = "new" Then
                    Debug.WriteLine($"tSubSub {cldName}  St:{cldLFname} ToSt:{stateTo}")
                    If Not validNewStates.Contains(stateTo) Then
                        newStatusFiles.Add(cldName)
                    End If
                End If

                If cldName = "*********.iam" Then
                    Debugger.Break()
                End If

                ' Check if the referenced file is the latest version
                Dim isLatest As Boolean = True
                If referencedFileId > 0 Then
                    ' We have a specific file ID that was referenced in the assembly
                    ' Check if this referenced file ID matches the latest file ID
                    isLatest = (referencedFileId = currFile.Id)
                    Debug.WriteLine($"Check {cldName} isLastest:{isLatest} (referencedFileId:{referencedFileId} vs latestFileId:{currFile.Id})")

                    ' If not latest, you might want to take some action or log this
                    If Not isLatest Then
                        Debug.WriteLine($"WARNING: {cldName} is NOT the latest version in the assembly!")
                        ' You could add this to a collection of outdated files if needed
                    End If
                Else
                    ' No specific file ID provided, assume it's latest (for root calls)
                    Debug.WriteLine($"Check {cldName} isLastest:{isLatest} (no referencedFileId provided, assuming latest)")
                End If

                If cldLFname = statefrom Or cldLFname = statefromfrom Then
                    Dim gridRow As String() = New String() {"", cldName, cldLFname, stateTo, masterId}
                    Debug.WriteLine($"[{DateTime.Now:HH:mm:ss}] tSubSub {cldName} St:{cldLFname}")
                    gridview.Rows.Add(gridRow)
                End If
                ' === Get all parent and child associations in one call ===
                Dim assocs As ACW.FileAssocLite() = m_conn.WebServiceManager.DocumentService.GetFileAssociationLitesByIds(
                {currFile.Id},
                 ACW.FileAssocAlg.LatestConsumable,
                FileAssociationTypeEnum.Dependency,
                False,
                FileAssociationTypeEnum.Dependency,
                False,
                False,
                False, 'True
                False)

                If assocs Is Nothing OrElse assocs.Length = 0 Then Return "ok"

                ' Collect all related file IDs (excluding current file)
                Dim relatedIds As New HashSet(Of Long)
                For Each assoc As ACW.FileAssocLite In assocs
                    If assoc.ParFileId <> currFile.Id Then relatedIds.Add(assoc.ParFileId)
                    If assoc.CldFileId <> currFile.Id Then relatedIds.Add(assoc.CldFileId)
                Next

                Dim relatedFiles As List(Of ACW.File) = New List(Of ACW.File)
                If relatedIds.Count > 0 Then
                    relatedFiles = m_conn.WebServiceManager.DocumentService.GetFilesByIds(relatedIds.ToArray()).ToList()
                End If

                ' Now process each association
                For Each assoc As ACW.FileAssocLite In assocs
                    If assoc.ParFileId = currFile.Id Then
                        ' This is a CHILD file (ParField is current file)
                        Dim childFile = relatedFiles.FirstOrDefault(Function(f) f.Id = assoc.CldFileId)
                        If childFile IsNot Nothing Then
                            ' Pass the actual file ID that's referenced in the assembly
                            traverseSubSub(childFile.MasterId, gridview, statefrom, statefromfrom, stateTo, assoc.CldFileId)
                        End If

                    ElseIf assoc.CldFileId = currFile.Id Then
                        ' This is a PARENT file (CldField is current file)
                        Dim parentFile = relatedFiles.FirstOrDefault(Function(f) f.Id = assoc.ParFileId)
                        If parentFile IsNot Nothing Then
                            Dim parName As String = parentFile.Name
                            Dim parNameBase As String = parName.Split("."c).First
                            Dim parLFname As String = parentFile.FileLfCyc.LfCycStateName
                            If (parLFname = statefrom Or parLFname = statefromfrom) AndAlso parNameBase = cldNameBase Then
                                Debug.WriteLine($"loop[{DateTime.Now:HH:mm:ss}] tSubSub {parName} St:{cldLFname}")
                                Dim gridRow As String() = New String() {"", parName, parLFname, stateTo, parentFile.MasterId}
                                gridview.Rows.Add(gridRow)
                            End If
                        End If
                    End If
                Next
            Catch ex As Exception
                Debug.WriteLine("Error in traverseSubSub: " & ex.Message)
            End Try
            Return "ok"
        End Function
        Function traverseSubSubOLD(masterId As Long, ByVal gridview As DataGridView, ByVal statefrom As String, ByVal statefromfrom As String, ByVal stateTo As String)
            Try
                'get info and parents of this file
                Dim currfile As ACW.File = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterId)
                'add info to grid
                Dim cldName As String = currfile.Name
                Dim cldnameBase As String = cldName.Split(".").First
                Dim cldLFname As String = currfile.FileLfCyc.LfCycStateName
                ' Debug.WriteLine("tSubSub " & cldnameBase & "St" & cldLFname)
                ' Check for "new" status - add to the same collection
                Dim validNewStates As String() = {"FI-WIP", "FI-For Review", "PL-WIP"}
                If cldLFname.ToLower = "new" Then
                    Debug.WriteLine($"tSubSub {cldName}  St:{cldLFname} ToSt:{stateTo}")
                    If Not validNewStates.Contains(stateTo) Then
                        newStatusFiles.Add(cldName)
                    End If
                End If
                'if file is PL-Released then exit
                If cldLFname.ToLower = "pl-released" Then Exit Function
                If cldLFname = statefrom Or cldLFname = statefromfrom Then ' is state from
                    'add child master ID to ChangeStateMasterId
                    Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] tSubSub {cldName} St:{cldLFname}")
                    Dim gridRow As String() = New String() {"", cldName, cldLFname, stateTo, masterId}
                    gridview.Rows.Add(gridRow)
                End If
                'find parents and add to the grid
                Dim fassocarr As FileAssocArray
                fassocarr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(masterId.ToSingleArray, FileAssociationTypeEnum.Dependency, False, FileAssociationTypeEnum.None, False, True, False, False).First
                If fassocarr.FileAssocs IsNot Nothing Then
                    For Each assoc As FileAssoc In fassocarr.FileAssocs
                        Dim parName As String = assoc.ParFile.Name
                        Dim parNameBase As String = Split(parName, ".").First
                        Dim parLFname As String = assoc.ParFile.FileLfCyc.LfCycStateName
                        If (parLFname = statefrom Or parLFname = statefromfrom) And cldnameBase = parNameBase Then
                            Debug.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] tSubSub {parName} St:{parLFname}")
                            Dim gridRow As String() = New String() {"", parName, parLFname, stateTo, assoc.ParFile.MasterId}
                            gridview.Rows.Add(gridRow)
                        End If
                    Next
                End If
                'direct childs

                fassocarr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(masterId.ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False, False).First
                'now we have master model and directt child models
                '   Dim i As Long = 1
                If fassocarr.FileAssocs IsNot Nothing Then
                    For Each fassoc As FileAssoc In fassocarr.FileAssocs
                        traverseSubSub(fassoc.CldFile.MasterId, gridview, statefrom, statefromfrom, stateTo)
                    Next
                Else
                End If
            Catch ex As Exception
            End Try
            Return "ok"
        End Function

        Private Sub fromToMatrixSub()
            ReDim fromToMatrix(8, 9)
            'from
            fromToMatrix(1, 0) = "New"
            fromToMatrix(2, 0) = "FI-WIP"
            fromToMatrix(3, 0) = "FI-For Review" 'New!
            fromToMatrix(4, 0) = "FI-Customer Approval"
            fromToMatrix(5, 0) = "FI-Released"
            fromToMatrix(6, 0) = "PL-Production"
            fromToMatrix(7, 0) = "PL-WIP"
            fromToMatrix(8, 0) = "PL-ForApproval"
            'to
            fromToMatrix(0, 1) = "New"
            fromToMatrix(0, 2) = "FI-WIP"
            fromToMatrix(0, 3) = "FI-For Review" ' New!
            fromToMatrix(0, 4) = "FI-Customer Approval"
            fromToMatrix(0, 5) = "FI-Released"
            fromToMatrix(0, 6) = "PL-Production"
            fromToMatrix(0, 7) = "PL-WIP"
            fromToMatrix(0, 8) = "PL-ForApproval"
            fromToMatrix(0, 9) = "PL-Released"
            fromToMatrix(1, 2) = True 'New -> FI-WIP, if user is FI
            ' fromToMatrix(1, 3) = True ' New -> "FI-For Review" , usually when selected is FI-WIP -> "FI-For Review" 
            fromToMatrix(1, 3) = True ' New -> New -> "FI-For Review", usually when selected is FI-WIP -> FI-Customer Approval
            fromToMatrix(1, 7) = True ' New -> PL-WIP, IF user is PL
            fromToMatrix(1, 8) = True ' New -> PL-ForApproval
            fromToMatrix(2, 3) = True ' FI-WIP -> FI-For Review
            ' fromToMatrix(2, 4) = True ' FI-WIP -> FI-Released, usually when selected is FI-Customer Approval -> FI-Released
            fromToMatrix(3, 4) = True ' FI-For Review -> FI-Customer Approval New!
            fromToMatrix(4, 5) = True ' FI-Customer Approval -> Fi-Released
            fromToMatrix(5, 6) = True ' FI-Released -> PL-Production
            fromToMatrix(6, 7) = True ' PL - Production -> PL-WIP
            fromToMatrix(7, 8) = True ' PL-WIP -> PL-ForApproval
            fromToMatrix(8, 9) = True ' PL-ForApproval -> PL-Released
        End Sub
        Private Sub usrLevelMatrixSub()
            ReDim usrLevelMatrix(3)
            usrLevelMatrix(0) = "FI-ENG"
            usrLevelMatrix(1) = "FI-ENG-SR"
            usrLevelMatrix(2) = "PL-ENG"
            usrLevelMatrix(3) = "PL-ENG-SR"
        End Sub
        Public Shared Sub Write_log(str_log_file As String, err_message As String)
            Try
                Using writer As StreamWriter = New StreamWriter(str_log_file, True)
                    writer.WriteLine("**************")
                    writer.WriteLine(DateTime.Now.ToString("dd-MM-yyyy") + "  :  " + DateTime.Now.ToLongTimeString + "  :  " + err_message)
                    writer.Close()
                End Using
            Catch ex As Exception
                MessageBox.Show(ex.Message)
            End Try
        End Sub
        Private Sub sym_test()
            Dim strVaulrServerName As String = "https://skfisrv017.seaking.local"
            Dim strVaultName As String = "SKGroupDEV"
            Dim strVaultUserName As String = "test"
            Dim strVaultPassword As String = "test"
            Dim results As VDF.Vault.Results.LogInResult
            'Dim codeBase As String = System.Reflection.Assembly.GetExecutingAssembly().CodeBase
            ''Dim uri As UriBuilder = New UriBuilder(codeBase)
            'Dim path As String = uri.UnescapeDataString(uri.Path)
            'Dim vaullogintype As String = StreamReader
            Dim m_conn2 As Connection
            Dim strVaultUser As String = ""
            If strVaultUser = "" Then 'windows auth
                results = VDF.Vault.Library.ConnectionManager.LogIn(strVaulrServerName, strVaultName, Nothing, Nothing, VDF.Vault.Currency.Connections.AuthenticationFlags.WindowsAuthentication, Nothing)
            Else
                results = VDF.Vault.Library.ConnectionManager.LogIn(strVaulrServerName, strVaultName, strVaultUserName, strVaultPassword, VDF.Vault.Currency.Connections.AuthenticationFlags.Standard, Nothing)
            End If
            'results = VDF.Vault.Library.ConnectionManager.LogIn(strVaulrServerName, strVaultName, strVaultUserName, strVaultPassword, VDF.Vault.Currency.Connections.AuthenticationFlags.Standard, Nothing)
            '' Dim settings As New VDF.Vault.Forms.Settings.LoginSettings
            '' settings.AutoLoginMode = VDFVF.Settings.LoginSettings.AutoLoginModeValues.RestoreAndExecute
            '   Dim ee As CommandItemEventArgs = SKCopy2Project.SKCopy2ProjectCommandExtension.ee
            '  m_conn = ee.Context.Application.Connection
            ''m_conn = VDF.Vault.Forms.Library.Login(settings)
            If results.Success Then
                m_conn2 = results.Connection
            End If
            If m_conn2 Is Nothing Then
                MessageBox.Show("Log into Vault First")
                Exit Sub
            End If
            Dim linkTypeOptions = ItemFileLnkTypOpt.Primary Or ItemFileLnkTypOpt.PrimarySub Or ItemFileLnkTypOpt.Secondary
            Dim adminSvc = m_conn2.WebServiceManager.AdminService
            Dim allUsers As User() = adminSvc.GetAllUsers()
            Dim allUserIds = allUsers.[Select](Function(u) u.Id).ToArray()
            Dim allGroups As Group() = adminSvc.GetAllGroups()
            Dim allGroupIds = allGroups.[Select](Function(g) g.Id).ToArray()
            Dim allGroupInfos As GroupInfo() = adminSvc.GetGroupInfosByGroupIds(allGroupIds)
            Dim userIdToGroupInfos = allUserIds.ToDictionary(Function(uid) uid, Function(uid) allGroupInfos.Where(Function(gi) gi.Users IsNot Nothing AndAlso gi.Users.Any(Function(u) u.Id = uid)))
            Dim sym_curr_user_groups = New List(Of String)
            Dim refs = New List(Of VDF.Vault.Currency.FileSystem.FileReference)()
            Dim test As String = m_conn2.WebServiceManager.AdminService.Session.User.Id
            'Dim test_user As String = m_conn.WebServiceManager.AdminService.Session.User.Id
            'test = "115"
            Dim test2 As String = ""
            For Each user In allUsers
                Console.WriteLine("User {0} Groups (Direct Membership):", user.Name)
                Dim groupInfosForUser = userIdToGroupInfos(user.Id)
                If user.Name.Contains("Jouni") Then
                    test2 = user.Name
                End If
                If user.Id = test Then
                    For Each groupInfoForUser In groupInfosForUser
                        Dim groupForUser As Group = groupInfoForUser.Group
                        Console.WriteLine(" Group {0}", groupForUser.Name)
                        sym_curr_user_groups.Add(groupForUser.Name)
                    Next
                End If
                Console.WriteLine()
            Next
        End Sub
        Private Sub FIorPLsub(e As CommandItemEventArgs)
            Dim _groups As ACW.Group()
            Dim _memberUsrs As ACW.User()
            usrLevelMatrixSub()
            Try
                m_conn = e.Context.Application.Connection
                Dim userid As Long = m_conn.UserID ' current user
                Dim currUsr As ACW.User = m_conn.WebServiceManager.AdminService.GetUserByUserId(userid)
                '  Dim usrinfo As UserInfo = m_conn.WebServiceManager.AdminService.GetUserInfoByUserId(userid)
                Try
                    Try
                        _groups = m_conn.WebServiceManager.AdminService.GetGroupsByNames("FI".ToSingleArray)
                        'if not error it user is part of FI
                        FIorPL = "FI"
                    Catch ex As Exception 'wtf im not Finnish?
                        Try
                            'otherwise try PL
                            _groups = m_conn.WebServiceManager.AdminService.GetGroupsByNames("PL".ToSingleArray)
                            FIorPL = "PL"
                        Catch ex
                        End Try
                    End Try
                    'roles
                    Try
                        For i As Integer = 0 To usrLevelMatrix.Count - 1
                            Try
                                _groups = m_conn.WebServiceManager.AdminService.GetGroupsByNames(usrLevelMatrix(i).ToSingleArray)
                                currUsrGroup = usrLevelMatrix(i)
                            Catch ex As Exception
                                'not the group
                            End Try
                        Next
                    Catch ex As Exception
                    End Try
                Catch ex As Exception
                End Try
            Catch ex As Exception
            End Try
hell:
        End Sub
        Private Sub getLFs(e As CommandItemEventArgs)
            Dim _LfCycStates As LfCycState()
            Try
                Dim _lfservice As LifeCycleService = e.Context.Application.Connection.WebServiceManager.LifeCycleService
                Dim transitionids As Long() = _lfservice.GetAllowedLifeCycleStateTransitionIds
                Dim _lfLfCycDefs As LfCycDef() = _lfservice.GetAllLifeCycleDefinitions
                'add lifecycles to matrix
                Dim i As Integer = 0
                For Each _lfCycDef As LfCycDef In _lfLfCycDefs
                    If _lfCycDef.DispName.ToLower = "SK-Normal-File".ToLower Then
                        _LfCycStates = _lfCycDef.StateArray
                        'For Each _LfCycState As LfCycState In _LfCycStates
                        For ii As Integer = 0 To _LfCycStates.Length - 1
                            'in order
                            For Each _LfCycState1 As LfCycState In _LfCycStates
                                'find correct version
                                If _LfCycState1.DispOrder = ii Then
                                    'print to matrix
                                    ReDim Preserve lfMatrix(1, i)
                                    lfMatrix(0, i) = _LfCycState1.DispName
                                    lfMatrix(1, i) = _LfCycState1.Id
                                    i += 1
                                    Exit For
                                End If
                            Next
                        Next
                        'Next
                    End If
                Next
            Catch ex As Exception
            End Try
        End Sub
        Private Sub getLFIs(e As CommandItemEventArgs)
            Dim _LfCycStates As LfCycState()
            Try
                Dim _lfservice As LifeCycleService = e.Context.Application.Connection.WebServiceManager.LifeCycleService
                Dim transitionids As Long() = _lfservice.GetAllowedLifeCycleStateTransitionIds
                Dim _lfLfCycDefs As LfCycDef() = _lfservice.GetAllLifeCycleDefinitions
                'add lifecycles to matrix
                Dim i As Integer = 0
                For Each _lfCycDef As LfCycDef In _lfLfCycDefs
                    If _lfCycDef.DispName.ToLower = "SK-Normal-Item".ToLower Then
                        _LfCycStates = _lfCycDef.StateArray
                        For Each _LfCycState As LfCycState In _LfCycStates
                            ReDim Preserve lfiMatrix(1, i)
                            lfiMatrix(0, i) = _LfCycState.DispName
                            lfiMatrix(1, i) = _LfCycState.Id
                            i += 1
                        Next
                    End If
                Next
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKCopyItemCommandHandler(s As Object, e As CommandItemEventArgs)
            'Copy item command
            Try
                ' The Context part of the event args tells us information about what is selected.
                ' Run some checks to make sure that the selection is valid.
                'try to set e as connection
                m_conn = e.Context.Application.Connection
                If e.Context.CurrentSelectionSet.Count() = 0 Then
                    MessageBox.Show("Nothing Is selected")
                Else
                    ' we only have one item selected, which is the expected behavior
                    Dim selectedfile As ACW.File = Nothing
                    Dim myform As New sk_copyitemF
                    Dim selections As List(Of ISelection) = e.Context.CurrentSelectionSet
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    'get ship code
                    Try
                        If selections.First.TypeId = SelectionTypeId.File Then
                            ' our ISelection.Id is really a File.MasterId
                            selectedfile = mgr.DocumentService.GetLatestFileByMasterId(selections.First.Id)
                        ElseIf selections.First.TypeId = SelectionTypeId.FileVersion Then
                            ' our ISelection.Id is really a File.Id
                            selectedfile = mgr.DocumentService.GetFileById(selections.First.Id)
                        End If
                        'selectedfile = mgr.DocumentService.GetLatestFileByMasterId(selections.First.Id)
                        myform.TextBoxShipFom.Text = selectedfile.Name.Split("-").First
                        selectedfile = Nothing
                    Catch ex As Exception
                    End Try
                    'check and eliminate every thing other than iams and dgns and ERPTYPE should be PAR_ITEM
                    For Each selection As ISelection In selections
                        If selection.TypeId = SelectionTypeId.File Then
                            ' our ISelection.Id is really a File.MasterId
                            selectedfile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                        ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                            ' our ISelection.Id is really a File.Id
                            selectedfile = mgr.DocumentService.GetFileById(selection.Id)
                        End If
                        Dim Shiplen As Integer = myform.TextBoxShipFom.Text.Length
                        If (selectedfile.Name.Split(".")(1) = "iam" Or selectedfile.Name.Split(".")(1) = "dgn") And myform.TextBoxShipFom.Text = Left(selectedfile.Name, Shiplen) Then
                            Dim erptypeval As String = Nothing
                            Try
                                erptypeval = GetPropertyValue(selectedfile.Id, "ERPTYPE", e, mgr)
                            Catch ex As Exception
                            End Try
                            If erptypeval IsNot Nothing And erptypeval.ToLower = "par_item" Or selectedfile.Name.Split(".")(1) = "dgn" Then
                                Dim row(8) As String
                                row(0) = selectedfile.MasterId
                                row(1) = selectedfile.Id
                                row(2) = selectedfile.Name
                                row(3) = selectedfile.FileLfCyc.LfCycStateName
                                Dim oFolder As Folder = mgr.DocumentService.GetFolderById(selectedfile.FolderId)
                                row(4) = oFolder.FullName
                                row(5) = oFolder.Name
                                row(6) = ""
                                If oFolder.Name.ToLower <> "furniture" Then
                                    row(6) = ""
                                    Dim oParentFolder As Folder = mgr.DocumentService.GetFolderById(oFolder.Id)
                                    Do Until oParentFolder.Name.ToLower = "furniture"
                                        If oParentFolder.Name.ToLower = "$" Then
                                            Continue For
                                        End If
                                        If row(6) = "" Then
                                            row(6) = oParentFolder.Name
                                        Else
                                            row(6) = oParentFolder.Name & "/" & row(6)
                                        End If
                                        oParentFolder = mgr.DocumentService.GetFolderById(oParentFolder.ParId)
                                    Loop
                                End If
                                row(7) = oFolder.Id
                                row(8) = ""
                                'has item?
                                Try
                                    Dim hasItem As ACW.Item() = mgr.ItemService.GetItemsByFileId(selectedfile.Id)
                                    If hasItem.Length <> 0 Then
                                        row(8) = "Has Item"
                                    End If
                                Catch ex As Exception
                                End Try
                                Dim gridrow As String() = New String() {row(0), row(1), row(2), row(3), row(4), row(5), row(6), row(7), row(8)}
                                myform.DataGridView1.Rows.Add(gridrow)
                            End If
                        End If
                    Next
                    If myform.DataGridView1.Rows.Count > 0 Then
                        myform.Button4.Enabled = True
                    End If
                    myform.Show()
                End If
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKCopy2ProjectAisiCommandHandler(s As Object, e As CommandItemEventArgs)
            Dim instRow As Double = Nothing
            Try
                ' The Context part of the event args tells us information about what is selected.
                ' Run some checks to make sure that the selection is valid.
                'try to set e as connection
                m_conn = e.Context.Application.Connection
                If e.Context.CurrentSelectionSet.Count() = 0 Then
                    MessageBox.Show("Nothing Is selected")
                ElseIf e.Context.CurrentSelectionSet.Count() > 1 Then
                    MessageBox.Show("This Function does Not support multiple selections")
                Else
                    ' we only have one item selected, which is the expected behavior
                    Dim selectedfile As Autodesk.Connectivity.WebServices.File = Nothing
                    Dim selection As ISelection = e.Context.CurrentSelectionSet.First()
                    exportselection = selection
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    ' Look of the File object.  How we do this depends on what is selected.
                    If selection.TypeId = SelectionTypeId.File Then
                        ' our ISelection.Id is really a File.MasterId
                        selectedfile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                    ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                        ' our ISelection.Id is really a File.Id
                        selectedfile = mgr.DocumentService.GetFileById(selection.Id)
                    End If
                    If selectedfile Is Nothing Then
                        MessageBox.Show("Selection Is Not a file.")
                    ElseIf selectedfile.Name.Split(".").Last <> "iam" And selectedfile.Name.Split(".").Last <> "ipt" Then
                        MessageBox.Show("Selected file Is Not assembly Or part")
                    Else
                        'go
                        Dim myform As New copy2ProjectAisi
                        exportselectedFile = selectedfile
                        myform.Show()
                    End If
                End If
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKCopy2ProjectCommandHandler(s As Object, e As CommandItemEventArgs)
            Dim instRow As Double = Nothing
            Try
                ' The Context part of the event args tells us information about what is selected.
                ' Run some checks to make sure that the selection is valid.
                'try to set e as connection
                m_conn = e.Context.Application.Connection
                If e.Context.CurrentSelectionSet.Count() = 0 Then
                    MessageBox.Show("Nothing Is selected")
                ElseIf e.Context.CurrentSelectionSet.Count() > 1 Then
                    MessageBox.Show("This Function does Not support multiple selections")
                Else
                    ' we only have one item selected, which is the expected behavior
                    Dim selectedfile As Autodesk.Connectivity.WebServices.File = Nothing
                    Dim selection As ISelection = e.Context.CurrentSelectionSet.First()
                    exportselection = selection
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    ' Look of the File object.  How we do this depends on what is selected.
                    If selection.TypeId = SelectionTypeId.File Then
                        ' our ISelection.Id is really a File.MasterId
                        selectedfile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                    ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                        ' our ISelection.Id is really a File.Id
                        selectedfile = mgr.DocumentService.GetFileById(selection.Id)
                    End If
                    If selectedfile Is Nothing Then
                        MessageBox.Show("Selection Is Not a file.")
                    ElseIf selectedfile.Name.Split(".").Last <> "iam" And selectedfile.Name.Split(".").Last <> "ipt" Then
                        MessageBox.Show("Selected file Is Not assembly Or part")
                    Else
                        'go
                        Dim myform As New Form1
                        exportselectedFile = selectedfile
                        myform.Show()
                    End If
                End If
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKPlace2InventorCmdItemCommandHandler(s As Object, e As CommandItemEventArgs)
            Try
                m_conn = e.Context.Application.Connection
                If e.Context.CurrentSelectionSet.Count() = 0 Then
                    MessageBox.Show("Nothing Is selected")
                ElseIf e.Context.CurrentSelectionSet.Count() > 1 Then
                    MessageBox.Show("This Function does Not support multiple selections")
                Else
                    ' we only have one item selected, which is the expected behavior
                    Dim selectedfile As Autodesk.Connectivity.WebServices.File = Nothing
                    Dim selection As ISelection = e.Context.CurrentSelectionSet.First()
                    exportselection = selection
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    ' Look of the File object.  How we do this depends on what is selected.
                    If selection.TypeId = SelectionTypeId.File Then
                        ' our ISelection.Id is really a File.MasterId
                        selectedfile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                    ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                        ' our ISelection.Id is really a File.Id
                        selectedfile = mgr.DocumentService.GetFileById(selection.Id)
                    End If
                    place2Inventor(selectedfile)
                End If
            Catch ex As Exception
            End Try
        End Sub
        Public Shared Sub place2Inventor(selectedfile As Autodesk.Connectivity.WebServices.File)
            Try
                ' The Context part of the event args tells us information about what is selected.
                ' Run some checks to make sure that the selection is valid.
                'go
                Try
                    If selectedfile Is Nothing Then
                        MessageBox.Show("Selection Is Not a file.")
                    ElseIf selectedfile.Name.Split(".")(1) <> "iam" And selectedfile.Name.Split(".")(1) <> "ipt" Then
                        MessageBox.Show("Selected file Is Not assembly Or part")
                    Else
                        ' selected file 
                        Dim folder As ACW.Folder = Nothing
                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(selectedfile.FolderId)
                        Dim fLocalName As String = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString & selectedfile.Name
                        Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, selectedfile)
                        '  downloadticket = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByMasterIds(newfile.MasterId.ToSingleArray()).First()
                        Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                        ' settings.CheckoutComment = "File Is CheckedOut By UpdatingReferences"
                        settings.LocalPath = Nothing
                        settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = True
                        settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = True
                        settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = True
                        settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                        settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download
                        settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                        Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)
                        ' next is inventor open?
                        Try
                            Dim oapp As Inventor.Application = System.Runtime.InteropServices.Marshal.GetActiveObject("Inventor.Application")
                            Try
                                Dim odoc As Inventor.AssemblyDocument = oapp.ActiveEditDocument
                                Try
                                    'place component 
                                    PlacePartInteractive(fLocalName, oapp)
                                    Try
                                        Dim App As Process() = Process.GetProcessesByName("inventor")
                                        If App.Length > 0 Then
                                            AppActivate(App(0).Id)
                                        End If
                                    Catch ex As Exception
                                    End Try
                                Catch ex As Exception
                                End Try
                            Catch ex As Exception
                                MessageBox.Show("Active document Is Not Assembly")
                            End Try
                        Catch ex As Exception
                            MessageBox.Show("Inventor Is Not open")
                            Exit Sub
                        End Try
                    End If
                Catch
                End Try
                '  End If
            Catch ex As Exception
            End Try
        End Sub
        Private Sub SKRename2StandardCmdItemCommandHandler(s As Object, e As CommandItemEventArgs)
            Try
                m_conn = e.Context.Application.Connection
                Try
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    Dim entityClassId As String = VDF.Vault.Currency.Entities.EntityClassIds.Files
                    Dim fNumchemes As NumSchm() = mgr.NumberingService.GetNumberingSchemes(entityClassId, NumSchmType.All)
                    For Each fNumcheme As ACW.NumSchm In fNumchemes
                        If fNumcheme.Name = "SK-Standard-Code" Then
                            SKstdNumberingcheme = fNumcheme.SchmID
                        End If
                    Next
                Catch ex As Exception
                End Try
                If e.Context.CurrentSelectionSet.Count() = 0 Then
                    MessageBox.Show("Nothing Is selected")
                ElseIf e.Context.CurrentSelectionSet.Count() > 1 Then
                    MessageBox.Show("This Function does Not support multiple selections")
                Else
                    Dim selectedfile As Autodesk.Connectivity.WebServices.File = Nothing
                    Dim selection As ISelection = e.Context.CurrentSelectionSet.First()
                    exportselection = selection
                    Dim mgr As WebServiceManager = e.Context.Application.Connection.WebServiceManager
                    ' Look of the File object.  How we do this depends on what is selected.
                    If selection.TypeId = SelectionTypeId.File Then
                        ' our ISelection.Id is really a File.MasterId
                        selectedfile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                    ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                        ' our ISelection.Id is really a File.Id
                        selectedfile = mgr.DocumentService.GetFileById(selection.Id)
                    End If
                    If selectedfile Is Nothing Then
                        MessageBox.Show("Selection Is Not a file.")
                    ElseIf selectedfile.Name.Substring(0, 1).ToLower <> "c" Or (selectedfile.Name.Split(".")(1) <> "iam" And selectedfile.Name.Split(".")(1) <> "ipt") Then
                        MessageBox.Show("Selected file Is Not a custom component Or Is Not a part Or an assembly")
                    Else
                        'go
                        Dim fassocArr As FileAssocArray = Nothing
                        Try
                            'is selected file released?
                            If selectedfile.FileLfCyc.Consume = True Then
                                MessageBox.Show("Selected file's state is Released.")
                            Else
                                'go further
                                Try
                                    'get parent relations
                                    fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(selectedfile.MasterId.ToSingleArray, FileAssociationTypeEnum.Dependency, False, FileAssociationTypeEnum.None, False, False, False, False).First
                                    'selected file name:
                                    Dim fName As String = selectedfile.Name.Split(".").First
                                    Dim fLength As Integer = fName.Length
                                    Dim i As Integer = 0
                                    'first let us add selected file to array
                                    ReDim Preserve filesMatrix(2, i)
                                    filesMatrix(0, i) = selectedfile.MasterId
                                    filesMatrix(1, i) = selectedfile.Id
                                    filesMatrix(2, i) = selectedfile.Name
                                    i += 1
                                    If fassocArr.FileAssocs IsNot Nothing Then
                                        For Each fAssoc As FileAssoc In fassocArr.FileAssocs
                                            'check parent files name 
                                            If fName = fAssoc.ParFile.Name.Substring(0, fLength) Then
                                                'add to array
                                                ReDim Preserve filesMatrix(2, i)
                                                filesMatrix(0, i) = fAssoc.ParFile.MasterId
                                                filesMatrix(1, i) = fAssoc.ParFile.Id
                                                filesMatrix(2, i) = fAssoc.ParFile.Name
                                                i += 1
                                            End If
                                        Next
                                    End If
                                    'create string combination
                                    Dim message As String = filesMatrix(2, 0) & vbCrLf
                                    For i = 1 To filesMatrix.GetLength(1) - 1
                                        message = message & vbTab & filesMatrix(2, i) & vbCrLf
                                    Next
                                    Dim result1 As DialogResult = MessageBox.Show(message, "Rename", MessageBoxButtons.OKCancel)
                                    If result1 = DialogResult.Cancel Then
                                        GoTo theEnd
                                    Else
                                        'rename
                                        Try
                                            'get new name
                                            Dim fieldinputs() As String = Nothing
                                            fieldinputs = "S".ToSingleArray()
                                            Dim newName As String = m_conn.WebServiceManager.DocumentService.GenerateFileNumber(SKstdNumberingcheme, fieldinputs)
                                            Dim mIDs As Long() = Nothing ' masterIdArray
                                            For i = 0 To filesMatrix.GetLength(1) - 1
                                                ReDim Preserve mIDs(i)
                                                mIDs(i) = filesMatrix(1, i)
                                                i += 1
                                            Next
                                            My.Computer.Clipboard.SetText(newName)
                                            i = 0
                                            '    For Each downloadticket In downloadTickets
                                            fassocArr = Nothing
                                            For i = 0 To filesMatrix.GetLength(1) - 1
                                                Dim tbom As Autodesk.Connectivity.WebServices.BOM = m_conn.WebServiceManager.DocumentService.GetBOMByFileId(filesMatrix(1, i))
                                                fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(CLng(filesMatrix(0, i)).ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False, False).First
                                                ' Dim parfile As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(CLng(filesMatrix(1, i)).ToSingleArray()).First
                                                Dim f As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(CLng(filesMatrix(1, i)).ToSingleArray()).First
                                                Dim fileassocparams As ACW.FileAssocParam() = Nothing
                                                Dim fileassocparamslist As New ArrayList
                                                Dim fi As Integer = 0
                                                If fassocArr.FileAssocs IsNot Nothing Then
                                                    For Each assoc As ACW.FileAssoc In fassocArr.FileAssocs
                                                        Dim param As New ACW.FileAssocParam
                                                        param.CldFileId = assoc.CldFile.Id
                                                        param.RefId = assoc.RefId
                                                        param.Source = assoc.Source
                                                        param.Typ = assoc.Typ
                                                        param.ExpectedVaultPath = assoc.ExpectedVaultPath
                                                        ReDim Preserve fileassocparams(fi)
                                                        fileassocparams(fi) = param
                                                        '  fileassocparamslist.Add(param)
                                                        fi += 1
                                                        param = Nothing
                                                    Next
                                                End If
                                                'fileassocparamlist ok
                                                'Properties
                                                Try
                                                    Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, f)
                                                    Dim downloadticket As ACW.ByteArray
                                                    Dim uploadTicket As Byte()
                                                    downloadticket = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByMasterIds(CLng(filesMatrix(0, i)).ToSingleArray()).First()
                                                    Dim fileProps As CtntSrcPropDef() = m_conn.WebServiceManager.FilestoreService.GetContentSourcePropertyDefinitions(downloadticket.Bytes, True)
                                                    Dim propSet = fileProps.Where(Function(n) n.Typ = DataType.[String])
                                                    Dim pnumber As String = newName ' new name w/o extension
                                                    Dim localpath As String = m_conn.WorkingFoldersManager.GetPathOfFileInWorkingFolder(fileIteration).ToString
                                                    Dim checkedOutFile As ACW.File = m_conn.WebServiceManager.DocumentService.CheckoutFile(fileIteration.EntityIterationId, CheckoutFileOptions.Master, My.Computer.Name.ToString, localpath, "test create new version", downloadticket)
                                                    Dim oPropWriterequests As New ACW.PropWriteRequests
                                                    Dim propWrites() As PropWriteReq = Nothing
                                                    Dim propsetI As Integer = 0
                                                    'let's get the erptype val
                                                    Dim strmyERPTYPE As String = Nothing
                                                    Try
                                                        'get value
                                                        Dim fileIter As VDF.Vault.Currency.Entities.FileIteration = Nothing
                                                        fileIter = New VDF.Vault.Currency.Entities.FileIteration(m_conn, f)
                                                        Dim myKeyValPair As KeyValuePair(Of String, VDFVCP.PropertyDefinition)
                                                        Dim myERPTYPE As VDFVCP.PropertyDefinition = Nothing
                                                        Dim propDef As VDFVCP.PropertyDefinition
                                                        Dim props As VDFVCP.PropertyDefinitionDictionary = m_conn.PropertyManager.GetPropertyDefinitions(
   VDF.Vault.Currency.Entities.EntityClassIds.Files,
       Nothing, VDFVCP.PropertyDefinitionFilter.IncludeAll)
                                                        For Each myKeyValPair In props
                                                            ' Property definition from KeyValuePair
                                                            propDef = myKeyValPair.Value()
                                                            ' Using the display name to identify
                                                            ' the PropertyDefinition
                                                            If propDef.DisplayName =
                         "ERPTYPE" Then
                                                                'It is the PropertyDefinition
                                                                myERPTYPE = propDef
                                                            End If
                                                        Next
                                                        strmyERPTYPE =
                                m_conn.PropertyManager.GetPropertyValue _
                                     (fileIter, myERPTYPE, Nothing)
                                                    Catch ex As Exception
                                                    End Try
                                                    For Each prop As CtntSrcPropDef In propSet
                                                        If prop.DispName = "Part Number" Then
                                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = pnumber
                                    }
                                                            ReDim Preserve propWrites(propsetI)
                                                            propWrites(propsetI) = propWrite
                                                            propsetI += 1
                                                            If propsetI = 4 Then
                                                                Exit For
                                                            End If
                                                        ElseIf prop.DispName = "ERPTYPE" And (strmyERPTYPE Like "PAR*" Or strmyERPTYPE IsNot Nothing) Then
                                                            Dim propWrite As New PropWriteReq() With {
                                                    .CanCreate = False,
                                                    .Moniker = prop.Moniker,
                                                    .Val = strmyERPTYPE.Replace("PAR", "FIX")
                                                }
                                                            ReDim Preserve propWrites(propsetI)
                                                            propWrites(propsetI) = propWrite
                                                            propsetI += 1
                                                            If propsetI = 4 Then
                                                                Exit For
                                                            End If
                                                        ElseIf prop.DispName = "ERPCODE" Then
                                                            Dim propWrite As New PropWriteReq() With {
                                                    .CanCreate = False,
                                                    .Moniker = prop.Moniker,
                                                    .Val = "32" & pnumber.Substring(1).Replace("320", "32")
                                                }
                                                            ReDim Preserve propWrites(propsetI)
                                                            propWrites(propsetI) = propWrite
                                                            propsetI += 1
                                                            If propsetI = 4 Then
                                                                Exit For
                                                            End If
                                                        ElseIf prop.DispName = "ERPGROUP" Then
                                                            Dim propWrite As New PropWriteReq() With {
                                                    .CanCreate = False,
                                                    .Moniker = prop.Moniker,
                                                    .Val = "3200"
                                                }
                                                            ReDim Preserve propWrites(propsetI)
                                                            propWrites(propsetI) = propWrite
                                                            propsetI += 1
                                                            If propsetI = 4 Then
                                                                Exit For
                                                            End If
                                                        End If
                                                    Next
                                                    If propWrites IsNot Nothing Then
                                                        oPropWriterequests.Requests = propWrites
                                                    End If
                                                    Dim results As ACW.PropWriteResults = Nothing
                                                    Dim Ext As String = f.Name.Split(".").Last
                                                    uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, Ext, True, oPropWriterequests, results)
                                                    m_conn.WebServiceManager.DocumentService.CheckinUploadedFile(fileIteration.EntityMasterId, "Part Number and ERPTYPE updated, Renamed from " & filesMatrix(2, i), False, DateTime.Now, fileassocparams, tbom,
                    True, newName & "." & filesMatrix(2, i).Split(".").Last, fileIteration.FileClassification, fileIteration.IsHidden, uploadTicket.ToByteArray())
                                                    '   m_conn.WebServiceManager.DocumentService.CheckinUploadedFile(CLng(filesMatrix(0, i)), "Properties Updated", False, Date.Today, fileassocparams, tbom, False, newName & "." & filesMatrix(2, i).Split(".").Last, f.FileClass, f.Hidden, uploadTicket)
                                                Catch ex As Exception
                                                End Try
                                                ''''Try
                                                ''''    'checkout file w/ download
                                                ''''    Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, f)
                                                ''''    Dim localPath1 As String = m_conn.WorkingFoldersManager.GetWorkingFolder(fileIteration).FullPath
                                                ''''    If Not System.IO.Directory.Exists(localPath1) Then
                                                ''''        System.IO.Directory.CreateDirectory(localPath1)
                                                ''''    End If
                                                ''''    Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                                ''''    settings.CheckoutComment = "File is CheckedOut for Rename"
                                                ''''    settings.LocalPath = New VDF.Currency.FolderPathAbsolute(localPath1)
                                                ''''    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = False
                                                ''''    settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = False
                                                ''''    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = False
                                                ''''    settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                                                ''''    settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download
                                                ''''    settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download + VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Checkout)
                                                ''''    Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)
                                                ''''    Dim localPath As String = m_conn.WorkingFoldersManager.GetWorkingFolder(fileIteration).ToString
                                                ''''    'Dim FilePath As String = System.IO.Path.Combine(localPath, f.Name)
                                                ''''    ' Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(FilePath)
                                                ''''    Dim fldrPath As VDF.Currency.FolderPathAbsolute = New VDF.Currency.FolderPathAbsolute(localPath)
                                                ''''    Dim filePath As String = System.IO.Path.Combine(fldrPath.ToString(), fileIteration.EntityName)
                                                ''''    Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(FilePath)
                                                ''''    ' newfile = m_conn.WebServiceManager.DocumentService.CheckinUploadedFile(CLng(filesMatrix(0, i)), "Renamed from " & filesMatrix(2, i), False, DateTime.Now, fileassocparams, tbom, True, newName & "." & filesMatrix(2, i).Split(".").Last, parfile.FileClass, parfile.Hidden, uploadTicket.ToByteArray())
                                                ''''    m_conn.FileManager.CheckinFile(fileIteration, "Renamed from: " & filesMatrix(2, i), False, fileassocparams,
                                                ''''                                                              tbom, False, newName & "." & filesMatrix(2, i).Split(".").Last,
                                                ''''                                    f.FileClass, f.Hidden, filePathAbs)
                                                ''''    fileassocparams = Nothing
                                                ''''Catch ex As Exception
                                                ''''End Try
                                                '''                                                
                                            Next i
                                            MessageBox.Show("New Standard name is: " & newName)
                                        Catch ex As Exception
                                        End Try
                                    End If
                                Catch ex As Exception
                                    MessageBox.Show(ex.ToString)
                                End Try
                            End If
                        Catch ex As Exception
                        End Try
                    End If
                    'Change category
                    Dim oCats As Cat() = Nothing
                    Dim oCat As Cat
                    Dim oCatID As Long
                    Try
                        oCats = m_conn.WebServiceManager.CategoryService.GetCategoriesByEntityClassId("FILE", True)
                        For Each oCat In oCats
                            If oCat.Name.ToLower = "sk-designed-standard" Then
                                oCatID = oCat.Id
                                'Exit For
                            End If
                        Next
                    Catch ex As Exception
                    End Try
                    Try
                        Dim fArr As Long() = Nothing
                        Dim cArr As Long() = Nothing
                        If oCatID <> Nothing Then
                            'create arr for fileMasteriD
                            For i = 0 To filesMatrix.GetLength(1) - 1
                                ReDim Preserve fArr(i)
                                fArr(i) = filesMatrix(0, i)
                            Next
                        End If
                        If fArr IsNot Nothing Then
                            For i As Integer = 0 To fArr.Length - 1
                                ReDim Preserve cArr(i)
                                cArr(i) = oCatID
                            Next
                        End If
                        m_conn.WebServiceManager.DocumentServiceExtensions.UpdateFileCategories(fArr, cArr, "Category Changed.")
                    Catch ex As Exception
                    End Try
                End If
            Catch ex As Exception
            End Try
theEnd:
            filesMatrix = Nothing
        End Sub
        Public Shared Sub PlacePartInteractive(ByVal filename As String, ByVal oapp As Inventor.Application)
            ' Define the filename to place. 
            ' Post the filename to the private event queue. 
            Dim cmdMgr As Inventor.CommandManager
            cmdMgr = oapp.CommandManager
            Call cmdMgr.PostPrivateEvent(Inventor.PrivateEventTypeEnum.kFileNameEvent, filename)
            ' Execute the "Place Component" command. 
            cmdMgr.ControlDefinitions.Item("AssemblyPlaceComponentCmd").Execute()
        End Sub
        ''' <summary>
        ''' This function is called whenever our custom tab is active and the selection has changed in the main grid.
        ''' </summary>
        ''' <param name="sender">The sender object.  Usually not used.</param>
        ''' <param name="e">The event args.  Provides additional information about the environment.</param>
        Private Sub propertyTab_SelectionChanged(sender As Object, e As SelectionChangedEventArgs)
            Try
                ' The event args has our custom tab object.  We need to cast it to our type.
                Dim tabControl As MyCustomTabControl = TryCast(e.Context.UserControl, MyCustomTabControl)
                ' Send selection to the tab so that it can display the object.
                'tabControl.SetSelectedObject(e.Context.SelectedObject)
            Catch ex As Exception
                ' If something goes wrong, we don't want the exception to bubble up to Vault Explorer.
                MessageBox.Show("Error: " & ex.Message)
            End Try
        End Sub
        Public Function GetPropertyValue(FId As Long, iProp As String, e As CommandItemEventArgs, mgr As WebServiceManager) As String
            Dim returnval As String = ""
            Try
                Dim m_conn As _
           VDF.Vault.Currency.Connections.Connection = e.Context.Application.Connection
                Dim childFile As ACW.File = mgr.DocumentService.GetFileById(FId)
                Dim fName As String = childFile.Name
                Dim props As VDF.Vault.Currency.Properties.PropertyDefinitionDictionary =
                                        m_conn.PropertyManager.GetPropertyDefinitions(
                                      VDF.Vault.Currency.Entities.EntityClassIds.Files,
                                          Nothing, VDF.Vault.Currency.Properties.PropertyDefinitionFilter.IncludeAll)
                Dim myKeyValPair As KeyValuePair _
                                                    (Of String, VDF.Vault.Currency.Properties.PropertyDefinition)
                Dim myUDP_iProp As VDF.Vault.Currency.Properties.PropertyDefinition _
                                                       = Nothing
                Dim propDef As VDF.Vault.Currency.Properties.PropertyDefinition
                For Each myKeyValPair In props
                    ' Property definition from KeyValuePair
                    propDef = myKeyValPair.Value()
                    ' Using the display name to identify
                    ' the PropertyDefinition
                    If propDef.DisplayName =
                                     iProp Then
                        'It is the PropertyDefinition
                        myUDP_iProp = propDef
                        Exit For
                    End If
                Next
                ' Get the FileIteration
                Dim fileIter As _
            VDF.Vault.Currency.Entities.FileIteration = Nothing
                fileIter = getFileIteration(fName, m_conn)
                Dim strPropertyVal As String
                If Not myUDP_iProp Is Nothing Then
                    strPropertyVal =
                        m_conn.PropertyManager.GetPropertyValue _
                             (fileIter, myUDP_iProp, Nothing)
                    If Not strPropertyVal Is Nothing Then
                        returnval = strPropertyVal
                    End If
                End If
            Catch ex As Exception
            End Try
            Return returnval
        End Function
        Public Function getFileIteration(nameOfFile As String, connection As VDF.Vault.Currency.Connections.Connection) As VDF.Vault.Currency.Entities.FileIteration
            Try
                Dim conditions As SrchCond()
                ReDim conditions(0)
                Dim lCode As Long = 1
                Dim Defs As PropDef() =
              connection.WebServiceManager.
                           PropertyService.
            GetPropertyDefinitionsByEntityClassId("FILE")
                Dim Prop As PropDef = Nothing
                For Each def As PropDef In Defs
                    If def.DispName =
                              "File Name" Then
                        Prop = def
                    End If
                Next def
                Dim searchCondition As _
            SrchCond = New SrchCond()
                searchCondition.PropDefId =
                                      Prop.Id
                searchCondition.PropTyp =
          PropertySearchType.SingleProperty
                searchCondition.SrchOper = lCode
                searchCondition.SrchTxt = nameOfFile
                conditions(0) = searchCondition
                ' search for files
                Dim FileList As List _
            (Of Autodesk.Connectivity.WebServices.File) =
                New List _
            (Of Autodesk.Connectivity.WebServices.File) '()
                Dim sBookmark As String = String.Empty
                Dim Status As SrchStatus = Nothing
                While (Status Is Nothing OrElse
                     FileList.Count < Status.TotalHits)
                    Dim files As Autodesk.Connectivity.
            WebServices.File() = connection.WebServiceManager.
            DocumentService.FindFilesBySearchConditions _
                                         (conditions,
                        Nothing, Nothing, True, True,
                                     sBookmark, Status)
                    If (Not files Is Nothing) Then
                        FileList.AddRange(files)
                    End If
                End While
                Dim oFileIteration As _
                    VDF.Vault.Currency.Entities.
                           FileIteration = Nothing
                For i As Integer =
                          0 To FileList.Count - 1
                    If FileList(i).Name =
                                  nameOfFile Then
                        oFileIteration =
                       New VDF.Vault.Currency.
            Entities.FileIteration(connection,
                                    FileList(i))
                    End If
                Next
                Return oFileIteration
            Catch ex As Exception
            End Try
        End Function
        Private Function getFileProperty(ByVal propName As String, ByVal selectedfile As ACW.File) As String
            'let's get selected files first linked file and make that for file where propery is got.
            Try
                Dim fassocArr As FileAssocArray = Nothing
                fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(selectedfile.MasterId.ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False, False).First
                Dim fid As Long = fassocArr.FileAssocs(0).CldFile.Id
                Dim f As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(fid.ToSingleArray()).First
                If f IsNot Nothing Then
                    selectedfile = f
                End If
            Catch ex As Exception
                ' MessageBox.Show("Mek:10 " & ex.Message)
            End Try
            'tehn property
            Try
                Dim props As VDFVCP.PropertyDefinitionDictionary =
                   m_conn.PropertyManager.GetPropertyDefinitions(
                 VDF.Vault.Currency.Entities.EntityClassIds.Files,
                     Nothing, VDFVCP.PropertyDefinitionFilter.IncludeAll)
                Dim myKeyValPair As KeyValuePair _
                 (Of String, VDFVCP.PropertyDefinition)
                Dim myFProperty As VDFVCP.PropertyDefinition _
                                       = Nothing
                Dim propDef As VDFVCP.PropertyDefinition
                For Each myKeyValPair In props
                    ' Property definition from KeyValuePair
                    propDef = myKeyValPair.Value()
                    ' Using the display name to identify
                    ' the PropertyDefinition
                    If propDef.DisplayName Like
                     propName Then
                        'It is the PropertyDefinition
                        myFProperty = propDef
                        Exit For
                    End If
                Next
                Dim fileIter As _
VDF.Vault.Currency.Entities.FileIteration = Nothing
                fileIter = New VDF.Vault.Currency.
Entities.FileIteration(m_conn,
                        selectedfile)
                Dim strmyFproperty As String = Nothing
                Try
                    strmyFproperty = m_conn.PropertyManager.GetPropertyValue _
                                                         (fileIter, myFProperty, Nothing).ToString
                Catch ex As Exception
                    Err.Clear()
                End Try
                If strmyFproperty IsNot Nothing Then
                    Return strmyFproperty
                Else
                    Return ""
                End If
            Catch ex As Exception
                MessageBox.Show("Mek:11 " & ex.Message)
            End Try
        End Function
        Private Function CheckForNewStateSubparts(masterId As Long) As Boolean
            ' Check if any subpart has 'New' state
            Try
                ' Get the latest file by master ID first
                Dim latestFile As ACW.File = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterId)
                Dim fileIds() As Long = {latestFile.Id}
                ' Use GetFileAssociationLitesByIds instead of GetLatestFileAssociationsByMasterIds
                Dim fileAssocs As ACW.FileAssocLite() = m_conn.WebServiceManager.DocumentService.GetFileAssociationLitesByIds(
                    fileIds,
                    ACW.FileAssocAlg.LatestConsumable,
                    ACW.FileAssociationTypeEnum.None,
                    False,
                    ACW.FileAssociationTypeEnum.Dependency,
                    True,  ' Include children
                    False, ' Don't include parents
                    False, ' Don't include siblings
                    False) ' Don't include where used

                If fileAssocs IsNot Nothing AndAlso fileAssocs.Length > 0 Then
                    For Each assoc As ACW.FileAssocLite In fileAssocs
                        ' Get the child file
                        Dim childFile As ACW.File = m_conn.WebServiceManager.DocumentService.GetFileById(assoc.CldFileId)
                        ' Check if state is 'New'
                        If childFile.FileLfCyc.LfCycStateName.ToLower = "new" Then
                            Return True
                        End If
                        ' Recursively check subparts
                        If CheckForNewStateSubparts(childFile.MasterId) Then
                            Return True
                        End If
                    Next
                End If
                Return False
            Catch ex As Exception
                MessageBox.Show(ex.Message & " Error checking subpart states")
                Return False
            End Try
        End Function
    End Class
End Namespace
